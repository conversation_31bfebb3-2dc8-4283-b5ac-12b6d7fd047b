import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FertilityDietPlan from "./FertilityDietPlan";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PageHeaderProvider>{component}</PageHeaderProvider>);
};

describe("FertilityDietPlan", () => {
  it("renders the page title", () => {
    renderWithProvider(<FertilityDietPlan />);
    expect(screen.getByText("Diet Assessment")).toBeInTheDocument();
  });

  it("renders all form fields", () => {
    renderWithProvider(<FertilityDietPlan />);
    expect(screen.getByLabelText("Age")).toBeInTheDocument();
    expect(screen.getByLabelText("Weight (kg)")).toBeInTheDocument();
    expect(screen.getByLabelText("AMH Level (Optional)")).toBeInTheDocument();
  });

  it("allows input in form fields", () => {
    renderWithProvider(<FertilityDietPlan />);
    const ageInput = screen.getByLabelText("Age");
    fireEvent.change(ageInput, { target: { value: "30" } });
    expect(ageInput).toHaveValue(30);
    const weightInput = screen.getByLabelText("Weight (kg)");
    fireEvent.change(weightInput, { target: { value: "60" } });
    expect(weightInput).toHaveValue(60);
    const amhInput = screen.getByLabelText("AMH Level (Optional)");
    fireEvent.change(amhInput, { target: { value: "2.5" } });
    expect(amhInput).toHaveValue(2.5);
  });

  it("toggles sex selection", () => {
    renderWithProvider(<FertilityDietPlan />);
    const pcosBtn = screen.getByRole("button", { name: "PCOS" });
    const lowAmhBtn = screen.getByRole("button", { name: "Low AMH" });
    expect(pcosBtn).toHaveClass("bg-[var(--violet-6)]");
    fireEvent.click(lowAmhBtn);
    expect(lowAmhBtn).toHaveClass("bg-[var(--violet-6)]");
  });

  it("calls button actions", () => {
    renderWithProvider(<FertilityDietPlan />);
    const calcBtn = screen.getByRole("button", {
      name: /Calculate BMR & Continue/i,
    });
    fireEvent.click(calcBtn);
    expect(calcBtn).toBeInTheDocument();
    const fertBtn = screen.getByRole("button", {
      name: /Increase Fertility Naturally/i,
    });
    fireEvent.click(fertBtn);
    expect(fertBtn).toBeInTheDocument();
    const ironBtn = screen.getByRole("button", { name: /Increase Iron/i });
    fireEvent.click(ironBtn);
    expect(ironBtn).toBeInTheDocument();
  });
});
