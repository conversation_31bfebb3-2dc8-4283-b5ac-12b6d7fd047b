import React, { useState } from "react";
import Header, { HeaderState } from "@/components/shared/Header/Header";
import Footer from "@/components/shared/Footer/Footer";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import <PERSON>Help from "@/components/shared/NeedHelp/NeedHelp";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import FormError from "@/components/shared/FormError/FormError";
import ErrorMessage from "@/components/shared/ErrorMessage/ErrorMessage";
import WarningAlert from "@/components/shared/WarningAlert/WarningAlert";
import DynamicFormField from "./DynamicFormField";
import { FormPageProps, Question } from "@/types/questions";
import PageLoader from "@/components/shared/PageLoader";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useDynamicForm } from "@/hooks/useDynamicForm";

interface DynamicFormPageProps extends FormPageProps {
  title: string;
  currentStep: number;
  totalSteps: number;
  questions: Question[];
  loading: boolean;
  error: string | null;
  submitButtonText: string;
  category?: string;
}

const DynamicFormPage: React.FC<DynamicFormPageProps> = ({
  title,
  currentStep,
  totalSteps,
  questions,
  loading,
  error,
  onNext,
  className = "",
  isSubmitting = false,
  errorMessage,
  submitButtonText,
  category,
}) => {
  const [showWarningAlert, setShowWarningAlert] = useState(false);

  const { user, signOut } = useAuth();
  const router = useRouter();
  const isLoggedIn = !!user;

  const handleFormSubmit = (submissionData: Record<string, string>) => {
    if (onNext && !isSubmitting) {
      onNext(submissionData);
    }
  };

  const {
    formData,
    errors,
    showValidation,
    renderList,
    handleInputChange,
    handleSubmit,
    isFormValid,
    isQuestionVisible,
  } = useDynamicForm(questions, handleFormSubmit, title, user?.id, category);

  // Handle header navigation (Login/Logout click)
  const handleHeaderNavigation = () => {
    setShowWarningAlert(true);
  };

  // Handle warning alert actions
  const handleWarningCancel = () => {
    setShowWarningAlert(false);
  };

  const handleWarningProceed = async () => {
    setShowWarningAlert(false);

    if (isLoggedIn) {
      // If user is logged in, sign them out first
      await signOut();
    }

    // Then redirect to login page with current path as redirect
    const currentPath = window.location.pathname;
    router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.LOGIN} onClick={handleHeaderNavigation} />

      <main className="flex-1 flex justify-center py-4 px-6 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[39.625rem] w-full">
          <StepHeader
            currentStep={currentStep}
            totalSteps={totalSteps}
            title={title}
          />

          <div className="bg-white rounded-lg p-6">
            {loading && <PageLoader />}

            {error && (
              <FormError
                error={error}
                onRetry={() => window.location.reload()}
              />
            )}

            {!loading && !error && questions.length > 0 && (
              <>
                <form className="space-y-6">
                  {renderList.map((item) => {
                    if ("isGroup" in item) {
                      return (
                        <div key={item.id} className="pt-4">
                          <h3 className="text-lg font-semibold text-[var(--grey-8)]">
                            {item.text}
                          </h3>
                        </div>
                      );
                    }
                    const question = item;
                    if (!isQuestionVisible(question, formData)) {
                      return null;
                    }
                    return (
                      <DynamicFormField
                        key={question.id}
                        question={question}
                        value={formData[question.id] || ""}
                        onChange={(value) =>
                          handleInputChange(question.id, value)
                        }
                        error={showValidation && !!errors[question.id]}
                        errorMessage={
                          showValidation ? errors[question.id] : undefined
                        }
                        required={question.is_mandatory}
                      />
                    );
                  })}

                  {/* Submission Error Display */}
                  {errorMessage && <ErrorMessage message={errorMessage} />}

                  {/* Validation Error Message */}
                  {showValidation && !isFormValid && (
                    <ErrorMessage
                      message="Please fill in all required fields to continue."
                      className="mt-4"
                    />
                  )}

                  {/* Next Step Button */}
                  <div className="pt-4">
                    <Button
                      type={ButtonType.PRIMARY}
                      text={isSubmitting ? "Saving..." : submitButtonText}
                      onClick={handleSubmit}
                      className="h-[3.125rem]"
                      disabled={isSubmitting}
                      icon={
                        isSubmitting ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polyline points="9,18 15,12 9,6"></polyline>
                          </svg>
                        )
                      }
                    />
                  </div>
                </form>
              </>
            )}

            {!loading && !error && questions.length === 0 && (
              <div className="text-center py-8">
                <div className="text-[var(--grey-6)] text-base">
                  No questions available
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />

      {/* Warning Alert Modal */}
      {showWarningAlert && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-md w-full">
            <WarningAlert
              title="Warning"
              description="You are in the middle of an assessment. On leaving this page, your session will be lost. Are you sure you want to proceed?"
              buttons={[
                {
                  text: "Cancel",
                  type: ButtonType.SECONDARY,
                  onClick: handleWarningCancel,
                },
                {
                  text: "Proceed",
                  type: ButtonType.PRIMARY,
                  onClick: handleWarningProceed,
                },
              ]}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DynamicFormPage;
