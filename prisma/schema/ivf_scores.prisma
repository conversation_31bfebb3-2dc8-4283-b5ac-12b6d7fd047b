model ivf_scores {
  id                          String   @id @default(uuid())
  user_id                     String   @unique @db.Uuid
  user                        profiles @relation(fields: [user_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)

  // Dynamic data storage
  form_data                   Json     @db.Json // Stores all form responses as JSON
  
  // Track user's current step in the IVF score process
  current_step                Int      @default(1) // 1: Step 1, 2: Step 2, 3: Step 3

  selected_track              String?  // Stores the selected track (T1, T2, T3)

  // Status tracking
  status                      ivf_assessment_status @default(pending)

  created_at                  DateTime @default(now())
  updated_at                  DateTime @updatedAt
}


enum menstrual_regularity {
  regular
  irregular
}

enum infertility_duration {
  under_6_months
  six_to_twelve_months
  over_12_months
}

enum diet_type {
  balanced
  vegetarian
  junk_heavy
  skipping_meals
}

enum exercise_frequency {
  daily
  two_to_three_times
  rarely_or_never
}

enum income_range {
  under_10k
  from_10k_to_50k
  from_50k_to_1l
  above_1l
}

enum living_area {
  urban
  semi_urban
  rural
}

enum stress_level_enum {
  low
  medium
  high
}

enum pollution_exposure {
  high
  moderate
  low
}

enum occupation_type {
  desk_job
  field_work
  night_shift
  homemaker
}

enum ivf_assessment_status {
  pending
  completed
  failed
}
