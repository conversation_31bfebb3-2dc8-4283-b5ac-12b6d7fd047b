model diet_plans {
  id                    String   @id @default(uuid())
  doctor_id             String   @db.Uuid
  doctor                profiles @relation("DoctorDietPlans", fields: [doctor_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)
  
  user_id               String   @db.Uuid
  user                  profiles @relation("UserDietPlans", fields: [user_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)
  
  diet_plan_scores_id   String   @db.Uuid
  diet_plan_scores      diet_plan_scores @relation(fields: [diet_plan_scores_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  
  target_calories       Int?
  condition_note        String?
  meal_plan_doc         String?
  
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
}

// New model for condition-based meal plans
model condition_meal_plans {
  id                    String   @id @default(uuid())
  doctor_id             String   @db.Uuid
  doctor                profiles @relation("DoctorConditionMealPlans", fields: [doctor_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)
  
  condition             diet_condition
  target_calories_min   Int
  target_calories_max   Int
  
  // 7-day meal plan data
  day_1_breakfast       Json     @db.Json
  day_1_lunch           Json     @db.Json
  day_1_dinner          Json     @db.Json
  day_1_snacks          Json     @db.Json
  
  day_2_breakfast       Json     @db.Json
  day_2_lunch           Json     @db.Json
  day_2_dinner          Json     @db.Json
  day_2_snacks          Json     @db.Json
  
  day_3_breakfast       Json     @db.Json
  day_3_lunch           Json     @db.Json
  day_3_dinner          Json     @db.Json
  day_3_snacks          Json     @db.Json
  
  day_4_breakfast       Json     @db.Json
  day_4_lunch           Json     @db.Json
  day_4_dinner          Json     @db.Json
  day_4_snacks          Json     @db.Json
  
  day_5_breakfast       Json     @db.Json
  day_5_lunch           Json     @db.Json
  day_5_dinner          Json     @db.Json
  day_5_snacks          Json     @db.Json
  
  day_6_breakfast       Json     @db.Json
  day_6_lunch           Json     @db.Json
  day_6_dinner          Json     @db.Json
  day_6_snacks          Json     @db.Json
  
  day_7_breakfast       Json     @db.Json
  day_7_lunch           Json     @db.Json
  day_7_dinner          Json     @db.Json
  day_7_snacks          Json     @db.Json
  
  nutritional_advice    String?
  is_active             Boolean  @default(true)
  
  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
}

enum diet_condition {
  PCOS
  LOW_AMH
  INCREASE_FERTILITY_NATURALLY
  INCREASE_IRON
}
