"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Plus, Edit3 } from "lucide-react";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { Checkbox } from "@/components/ShadcnUI/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ShadcnUI/dialog";
import type { RoleWithPermissions } from "@/types/roles-permissions";
import { AVAILABLE_RESOURCES, PERMISSION_ACTIONS } from "@/lib/utils/roles-permissions.utils";


// Quick Role Form Component
export function QuickRoleForm({ createRoleAction }: { createRoleAction: (formData: FormData) => Promise<void> }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          Create Role
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Role</DialogTitle>
          <DialogDescription>
            Create a basic role. You can assign permissions later.
          </DialogDescription>
        </DialogHeader>
        <form action={createRoleAction} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Role Name</Label>
            <Input
              id="name"
              name="name"
              placeholder="Enter role name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter role description"
              rows={3}
            />
          </div>
          <div className="flex justify-end gap-2">
            <DialogTrigger asChild>
              <Button type="button" variant="outline">Cancel</Button>
            </DialogTrigger>
            <Button type="submit">Create Role</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Advanced Role Form Component
export function AdvancedRoleForm({ createRoleWithPermissionsAction }: { createRoleWithPermissionsAction: (formData: FormData) => Promise<void> }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          Create with Permissions
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Role with Permissions</DialogTitle>
          <DialogDescription>
            Create a new role and assign permissions across different resources.
          </DialogDescription>
        </DialogHeader>
        <form action={createRoleWithPermissionsAction} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="adv-name">Role Name</Label>
              <Input
                id="adv-name"
                name="name"
                placeholder="Enter role name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="adv-description">Description</Label>
              <Input
                id="adv-description"
                name="description"
                placeholder="Enter role description"
              />
            </div>
          </div>
          
          {/* Permissions Grid */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Permissions</h4>
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">Resource</TableHead>
                    <TableHead className="text-center w-20">Create</TableHead>
                    <TableHead className="text-center w-20">Read</TableHead>
                    <TableHead className="text-center w-20">Update</TableHead>
                    <TableHead className="text-center w-20">Delete</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {AVAILABLE_RESOURCES.map((resource) => (
                    <TableRow key={resource}>
                      <TableCell className="font-medium capitalize">
                        {resource.replace('_', ' ')}
                      </TableCell>
                      {PERMISSION_ACTIONS.map((action) => (
                        <TableCell key={action} className="text-center">
                          <Checkbox
                            name={`${resource}_${action}`}
                            className="mx-auto"
                          />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
          
          <div className="flex justify-end gap-2">
            <DialogTrigger asChild>
              <Button type="button" variant="outline">Cancel</Button>
            </DialogTrigger>
            <Button type="submit">Create Role</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Edit Role Form Component
export function EditRoleForm({ 
  role, 
  updateRoleAction 
}: { 
  role: RoleWithPermissions; 
  updateRoleAction: (formData: FormData) => Promise<void>;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="Edit Role">
          <Edit3 className="h-3 w-3" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Role</DialogTitle>
          <DialogDescription>
            Update the role name and description.
          </DialogDescription>
        </DialogHeader>
        <form action={updateRoleAction} className="space-y-4">
          <input type="hidden" name="id" value={role.id.toString()} />
          <div className="space-y-2">
            <Label htmlFor={`edit-name-${role.id}`}>Role Name</Label>
            <Input
              id={`edit-name-${role.id}`}
              name="name"
              defaultValue={role.name || ''}
              placeholder="Enter role name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor={`edit-description-${role.id}`}>Description</Label>
            <Textarea
              id={`edit-description-${role.id}`}
              name="description"
              defaultValue={role.description || ''}
              placeholder="Enter role description"
              rows={3}
            />
          </div>
          <div className="flex justify-end gap-2">
            <DialogTrigger asChild>
              <Button type="button" variant="outline">Cancel</Button>
            </DialogTrigger>
            <Button type="submit">Update Role</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 