"use client";

import React from "react";
import VerifyAccountPage from "@/components/VerifyAccountPage/VerifyAccountPage";
import { useSearchParams } from "next/navigation";

export default function RegisterVerifyAccountPage() {
  // Render component; VerifyAccountPage will handle the actual redirect after OTP
  // We keep search params available in the URL so the component can read them if needed later
  useSearchParams();
  return <VerifyAccountPage />;
}
