model diet_plan_scores {
  id           String           @id @default(uuid())
  user_id      String           @unique @db.Uuid
  form_data    Json             @db.Json
  current_step Int              @default(1)
  status       diet_plan_status @default(pending)
  created_at   DateTime         @default(now())
  updated_at   DateTime         @updatedAt
  user         profiles         @relation(fields: [user_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)
}

enum diet_plan_status {
  pending
  completed
  failed
}
