import React from "react";
import { render, screen } from "@testing-library/react";
import BMRCalculation from "./BMRCalculation";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PageHeaderProvider>{component}</PageHeaderProvider>);
};

describe("BMRCalculation", () => {
  it("renders the main title", () => {
    renderWithProvider(<BMRCalculation />);
    expect(screen.getByText("BMR & Target Calories")).toBeInTheDocument();
  });

  it("renders the BMR value in the circle", () => {
    renderWithProvider(<BMRCalculation />);
    expect(screen.getByText("1,345")).toBeInTheDocument();
    expect(screen.getByText("kcal/day")).toBeInTheDocument();
  });

  it("renders both info cards", () => {
    renderWithProvider(<BMRCalculation />);
    expect(screen.getByText("Your BMR is:")).toBeInTheDocument();
    expect(screen.getByText("13.45 kcal/day")).toBeInTheDocument();
    expect(
      screen.getByText("Recommended Target Calories for IVF:")
    ).toBeInTheDocument();
    expect(screen.getByText("1,650 kcal/day")).toBeInTheDocument();
  });

  it("renders the condition note", () => {
    renderWithProvider(<BMRCalculation />);
    expect(screen.getByText("Condition Note")).toBeInTheDocument();
    expect(
      screen.getByText(
        /Based on your PCOS profile, we.ll focus on foods that stabilize insulin and boost hormone balance./i
      )
    ).toBeInTheDocument();
  });

  it("renders both action buttons", () => {
    renderWithProvider(<BMRCalculation />);
    expect(
      screen.getByRole("button", { name: /Download My 7-Days Plan/i })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /Book Nutrition Consult/i })
    ).toBeInTheDocument();
  });
});
