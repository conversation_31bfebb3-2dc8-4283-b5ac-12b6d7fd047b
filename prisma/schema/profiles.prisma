model profiles {
  id               BigInt            @id @default(autoincrement())
  created_at       DateTime          @default(now()) @db.Timestamptz(6)
  username         String?
  email            String?
  display_name     String?
  phone            String?
  address          String?
  auth_id          String?           @unique @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  updated_at       DateTime          @default(now()) @db.Timestamptz(6)
  age              Int?
  date_of_birth    DateTime?
  sex              Sex?
  diet_plan_scores diet_plan_scores?
  ivf_scores       ivf_scores?
  diet_plans_as_doctor diet_plans[] @relation("DoctorDietPlans")
  diet_plans_as_user   diet_plans[] @relation("UserDietPlans")
  condition_meal_plans_as_doctor condition_meal_plans[] @relation("DoctorConditionMealPlans")
}

enum Sex {
  male
  female
  other
}
