import React, { useState, useEffect } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import NeedHelp from "../shared/NeedHelp/NeedHelp";
import StepHeader from "../shared/StepHeader/StepHeader";
import CircularProgress from "./CircularProgress/CircularProgress";
import ProgressBar from "./ProgressBar/ProgressBar";
import { useAuth } from "@/contexts/AuthContext";
import { useScoreResults } from "@/hooks/useScoreResults";
import { useGuestScoreResults } from "@/hooks/useGuestScoreResults";
import ShimmerResultsPage from "../Shimmer/ShimmerResultsPage/ShimmerResultsPage";
import Modal from "../shared/Modal/Modal";
import { getGuestSessionToken } from "@/utils/guestSessionUtils";
import { useRouter } from "next/navigation";

// Email icon component
const EmailIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <polyline points="22,6 12,13 2,6" />
  </svg>
);

// ScoreResult interface is imported from useScoreResults hook

export interface SuccessResultsPageProps {
  onRetakeAssessment?: () => void;
  onVisitIVFCenter?: () => void;
  onShareEmail?: () => void;
  onHome?: () => void;
  onBookConsultation?: () => void;
  isSharing?: boolean;
  shareError?: string | null;
  shareSuccess?: boolean;
  isSubmitting?: boolean;
}

const SuccessResultsPage: React.FC<SuccessResultsPageProps> = ({
  onRetakeAssessment,
  onVisitIVFCenter,
  onShareEmail,
  onHome,
  isSharing = false,
  shareError = null,
  shareSuccess = false,
  isSubmitting = false,
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [sessionToken, setSessionToken] = useState<string | null>(null);
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Check if user is authenticated or guest
  const isAuthenticated = !!user;
  const isGuest = !isAuthenticated && !!sessionToken;

  // Use appropriate hook based on user type
  const authenticatedQuery = useScoreResults();
  const guestQuery = useGuestScoreResults(sessionToken);

  // Determine which query to use
  const {
    data: scoreResponse,
    isLoading,
    error,
    isError,
  } = isAuthenticated ? authenticatedQuery : guestQuery;

  // Get session token from storage for guest users
  useEffect(() => {
    if (!isAuthenticated) {
      const storedToken = getGuestSessionToken();
      if (storedToken) {
        setSessionToken(storedToken);
      }
    }
  }, [isAuthenticated]);

  // Handle back button navigation
  useEffect(() => {
    console.log("useEffect called with Score Status", scoreResponse?.status);
    // Only set up the back button handler if we have score data
    if (!scoreResponse) return;

    const handlePopState = (event: PopStateEvent) => {
      console.log(
        "handlePopState called with Score Status",
        scoreResponse?.status
      );
      // Check if the assessment is completed
      if (scoreResponse?.status === "completed") {
        // Prevent the default back navigation
        event.preventDefault();

        // Push the current state back to maintain the URL
        window.history.pushState(null, "", window.location.href);

        // Redirect to alert page
        router.push("/ivf-assessment/alert");
      }
      // If status is not "completed", allow normal back navigation
    };

    // Add event listener for popstate (back button)
    window.addEventListener("popstate", handlePopState);

    // Push a state to the history so we can catch the back button
    window.history.pushState(null, "", window.location.href);

    // Cleanup function
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [scoreResponse, router]);

  // Extract score data and log cache information
  const scoreResult = scoreResponse?.score || null;
  const avgFactors = scoreResult?.avgFactors || null;

  // Log performance information when data is available and store status
  React.useEffect(() => {
    if (scoreResponse?.score && scoreResponse?.status === "completed") {
      // Store the status in session storage
      sessionStorage.setItem("Result_status", scoreResponse.status || "");
      if (scoreResponse.cached) {
        console.log("Score results served from cache");
      } else {
        console.log(
          `Score results calculated in ${scoreResponse.processingTime}ms`
        );
      }
    }
  }, [scoreResponse]);

  // Combined loading state - for guest users, also wait for session token
  const loading = authLoading || isLoading || (isGuest && !sessionToken);

  // Helper function to get fertility band styles
  const getFertilityBandStyles = (band: string) => {
    if (band.includes("Excellent") || band.includes("good")) {
      return {
        dotColor: "var(--success-green-4)",
        bgColor: "var(--success-green-1)",
        textColor: "var(--success-green-4)",
      };
    } else if (band.includes("Good")) {
      return {
        dotColor: "var(--accent-blue-4)",
        bgColor: "var(--accent-blue-1)",
        textColor: "var(--accent-blue-4)",
      };
    } else if (band.includes("Fair") || band.includes("Moderate")) {
      return {
        dotColor: "var(--warning-yellow-4)",
        bgColor: "var(--warning-yellow-1)",
        textColor: "var(--warning-yellow-4)",
      };
    }
    // Default fallback
    return {
      dotColor: "white",
      bgColor: "var(--grey-6)",
      textColor: "white",
    };
  };

  // Get fertility band and styles based on score result
  const fertilityBand = scoreResult?.category || "Moderate Fertility";
  const bandStyles = getFertilityBandStyles(fertilityBand);

  // Format percentage for display
  const formattedPercentage = scoreResult
    ? scoreResult.percentage.toFixed(1)
    : "0.0";

  // Get suggestion text based on score category
  const getSuggestionText = (category: string) => {
    const lowercaseCategory = category.toLowerCase();
    if (
      lowercaseCategory.includes("high") ||
      lowercaseCategory.includes("good")
    ) {
      return "You&apos;re doing well! A few lifestyle changes and timely consultation could increase your chances.";
    } else if (
      lowercaseCategory.includes("low") ||
      lowercaseCategory.includes("poor")
    ) {
      return "Your fertility score indicates some concerns. We recommend consulting with a fertility specialist soon.";
    } else {
      return "Your fertility score is moderate. Some lifestyle changes and a consultation could help improve your chances.";
    }
  };

  // Check for guest users without session token
  if (!isAuthenticated && !sessionToken && !authLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header state={HeaderState.LOGIN} />
        <main className="flex-1 flex justify-center items-center">
          <div className="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
            <div className="text-[var(--error-red-4)] text-5xl mb-4">!</div>
            <h2 className="text-[var(--grey-7)] text-xl font-bold mb-4">
              No Results Found
            </h2>
            <p className="text-[var(--grey-6)] mb-6">
              We couldn&apos;t find your assessment results. Please complete the
              assessment first.
            </p>
            <Button
              type={ButtonType.PRIMARY}
              text="Take Assessment"
              onClick={
                onRetakeAssessment ||
                (() => (window.location.href = "/ivf-assessment/user-type"))
              }
            />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Loading state (covers both loading and authLoading)
  if (loading || authLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header state={HeaderState.LOGIN} />
        <main className="flex-1 flex justify-center items-center">
          <div className="text-center">
            <ShimmerResultsPage />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Error state
  if (isError && error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";

    return (
      <div className="min-h-screen flex flex-col">
        <Header state={HeaderState.LOGIN} />
        <main className="flex-1 flex justify-center items-center">
          <div className="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
            <div className="text-[var(--error-red-4)] text-5xl mb-4">!</div>
            <h2 className="text-[var(--grey-7)] text-xl font-bold mb-4">
              Error Loading Results
            </h2>
            <p className="text-[var(--grey-6)] mb-6">{errorMessage}</p>
            <Button
              type={ButtonType.PRIMARY}
              text="Go to Home"
              onClick={onHome || (() => (window.location.href = "/"))}
            />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 md:py-6">
        <div className="w-[22.125rem] md:w-[59.813rem]">
          <StepHeader
            currentStep={6}
            totalSteps={6}
            title="Check Your Fertility Meter"
          />

          <div className="bg-white rounded-lg">
            <div
              id="fertility-score-results"
              className="space-y-8 w-full flex flex-col justify-center mx-auto"
            >
              {/* Circular Progress Meter */}
              <div className="flex justify-center mt-14">
                <CircularProgress
                  percentage={scoreResult?.percentage || 0}
                  score={`${formattedPercentage}%`}
                  band={fertilityBand}
                  bandStyles={bandStyles}
                />
              </div>

              {/* Score Details */}
              <div className="text-center mb-8">
                <p className="text-[var(--grey-6)] text-base mb-4">
                  Here&apos;s Your Fertility Meter Score
                </p>
                <div className="flex items-center justify-center gap-6">
                  <div>
                    <span className="text-[var(--grey-6)] text-base">
                      Score{" "}
                    </span>
                    <span className="text-[var(--grey-7)] font-medium bg-[var(--grey-2)] px-2 py-1 rounded-sm">
                      {scoreResult
                        ? `${scoreResult.totalScore.toFixed(1)} / ${scoreResult.maxScore}`
                        : "0 / 100"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[var(--grey-6)] text-base">
                      Band{" "}
                    </span>
                    <span
                      className="font-medium px-2 py-1 rounded-sm inline-flex items-center gap-2"
                      style={{
                        backgroundColor: bandStyles.bgColor,
                        color: bandStyles.textColor,
                      }}
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: bandStyles.dotColor }}
                      ></div>
                      {fertilityBand}
                    </span>
                  </div>
                </div>
              </div>

              {/* Breakdown Highlights */}
              <div className="mb-8">
                <h2 className="text-[var(--grey-7)] text-base font-bold mb-6">
                  Breakdown Highlights:
                </h2>
                <div className="flex flex-col md:flex-row justify-between border-1 rounded-md border-[var(--grey-3)] pt-6 pb-[1.875rem] px-[3.75rem] shadow-[0_10px_15px_-3px_var(--grey-3),0_4px_6px_-4px_var(--grey-3)]">
                  {avgFactors &&
                    Object.entries(avgFactors).map(([key, value], index) => {
                      const arr = value as [number, number];
                      const colors = [
                        "var(--violet-11)",
                        "var(--warning-yellow-3)",
                        "var(--info-blue-3)",
                      ];
                      return (
                        <div
                          key={key}
                          className={`flex-1 pr-0 pl-6 md:pr-6 border-b ${index === Object.entries(avgFactors).length - 1 ? "" : "md:border-r"} md:border-b-0 border-[var(--grey-3)] md:pb-0 pb-7.5`}
                        >
                          <ProgressBar
                            label={key}
                            value={arr[0] * 100}
                            maxValue={100}
                            color={colors[index]}
                            animate={true}
                            showInPercentage={true}
                          />
                        </div>
                      );
                    })}
                </div>
              </div>

              {/* Suggestion Box */}
              <div className="bg-[var(--grey-2)] h-auto md:h-[10.375rem] px-[2.5rem] py-[1.25rem] rounded-lg mb-8 flex flex-col gap-6">
                <div>
                  <h3 className="text-[var(--grey-7)] text-base font-medium mb-3">
                    Suggestion Box:
                  </h3>
                  <p className="text-[var(--grey-6)] text-base font-medium">
                    {getSuggestionText(scoreResult?.category || "Moderate")}
                  </p>
                </div>
                {/* Action Buttons */}
                <div className="w-full md:w-[32.563rem] flex flex-col md:flex-row gap-4">
                  <Button
                    type={ButtonType.SECONDARY}
                    text="Retake Assessment"
                    onClick={onRetakeAssessment || (() => {})}
                    className="border border-[var(--grey-3)] text-[var(--grey-7)] hover:bg-[var(--grey-1)]"
                    disabled={isSubmitting}
                  />
                  <Button
                    type={ButtonType.SECONDARY}
                    text="Visit IVF Center Near You"
                    onClick={onVisitIVFCenter || (() => {})}
                    className="border border-[var(--grey-3)] text-[var(--grey-7)] hover:bg-[var(--grey-1)]"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              {/* Share and Navigation */}
              <div className="flex flex-col md:flex-row items-center justify-between mb-8 gap-4">
                <div className="flex items-center gap-4">
                  <h1 className="text-[var(--grey-6)] whitespace-nowrap text-base font-medium">
                    Share Report:
                  </h1>
                  <Button
                    type={ButtonType.SECONDARY}
                    icon={isSharing ? undefined : <EmailIcon />}
                    text={isSharing ? "Sending..." : undefined}
                    onClick={onShareEmail || (() => {})}
                    size="sm"
                    disabled={isSharing || isSubmitting}
                    className={`bg-transparent py-2 px-0 h-auto ${
                      shareError
                        ? "text-[var(--error-red-4)] hover:text-[var(--error-red-5)]"
                        : shareSuccess
                          ? "text-[var(--success-green-4)] hover:text-[var(--success-green-5)]"
                          : "text-[var(--grey-6)] hover:text-[var(--grey-7)]"
                    } hover:bg-transparent`}
                  />
                  {shareError && (
                    <p className="text-[var(--error-red-4)] text-sm mt-2">
                      {shareError}
                    </p>
                  )}
                  {shareSuccess && (
                    <p className="text-[var(--success-green-4)] text-sm mt-2">
                      Email sent successfully!
                    </p>
                  )}
                </div>
                <div className="flex justify-end w-full md:w-auto gap-4">
                  {isAuthenticated ? (
                    <Button
                      type={ButtonType.SECONDARY}
                      text="Go to IVF Scores"
                      onClick={() => router.push("/user/ivf-success-score")}
                      size="sm"
                      className="w-full h-[3.125rem] text-[var(--grey-7)]"
                      disabled={isSubmitting}
                    />
                  ) : (
                    <Button
                      type={ButtonType.SECONDARY}
                      text="Become a Member"
                      onClick={() => router.push("/register")}
                      size="sm"
                      className="w-full h-[3.125rem] text-[var(--grey-7)]"
                      disabled={isSubmitting}
                    />
                  )}
                  {/* Book Consultation Button */}
                  <Button
                    type={ButtonType.PRIMARY}
                    text="Book a Free Consultation"
                    onClick={() => setModalOpen(true)}
                    size="sm"
                    className="w-full h-[3.125rem]"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
      <Modal
        open={modalOpen}
        title="🎉 Thank You for Reaching Out!"
        message="Your request for a free consultation has been received. One of our experienced doctors will connect with you shortly to guide you further.
In the meantime, feel free to explore your IVF journey or check helpful resources in your dashboard."
        onClose={() => setModalOpen(false)}
      />
    </div>
  );
};

export default SuccessResultsPage;
