import Image from "next/image";
import React from "react";

export interface NeedHelpProps {
  phoneNumber?: string;
  email?: string;
  onPhoneClick?: (phoneNumber: string) => void;
  onEmailClick?: (email: string) => void;
  className?: string;
}

const NeedHelp: React.FC<NeedHelpProps> = ({
  phoneNumber = "+91-9990044555",
  email = "<EMAIL>",
  onPhoneClick,
  onEmailClick,
  className = "",
}) => {
  const handlePhoneClick = () => {
    if (onPhoneClick) {
      onPhoneClick(phoneNumber);
    } else {
      // Default behavior: open phone dialer
      window.location.href = `tel:${phoneNumber}`;
    }
  };

  const handleEmailClick = () => {
    if (onEmailClick) {
      onEmailClick(email);
    } else {
      window.location.href = `mailto:${email}`;
    }
  };
  return (
    <div className={`text-left ${className} flex flex-col items-start`}>
      <p className="text-[var(--grey-5)] text-base mb-2">Need Help?</p>
      <div className="flex flex-col md:flex-row gap-2 whitespace-nowrap">
        <button
          type="button"
          onClick={handlePhoneClick}
          className="text-[var(--grey-7)] text-left text-lg md:text-xl font-medium hover:text-[var(--red-6)] transition-colors duration-200 rounded py-1"
        >
          {phoneNumber}
        </button>

        <Image
          src="/assets/Ellipse-6.svg"
          alt="Ellipse"
          width={10}
          height={10}
          className="hidden md:inline"
        />
        <button
          type="button"
          onClick={handleEmailClick}
          className="text-[var(--grey-7)] text-left text-lg md:text-xl font-medium hover:text-[var(--red-6)] transition-colors duration-200 rounded py-1"
        >
          {email}
        </button>
      </div>
    </div>
  );
};

export default NeedHelp;
