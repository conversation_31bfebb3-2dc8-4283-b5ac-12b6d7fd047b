import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardH<PERSON>er,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { FileText, PlusCircle } from "lucide-react";
import Link from "next/link";

export default function DietPlansPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Diet Plans</h1>
            <p className="text-muted-foreground">
              Manage condition-based meal plans for different fertility conditions
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Link href="/admin/diet-plans/create">
            <Button
              type={ButtonType.PRIMARY}
              size="sm"
              text="Add Meal Plan"
              icon={<PlusCircle className="h-4 w-4" />}
              className="gap-2 whitespace-nowrap"
            />
          </Link>
        </div>
      </div>

      {/* Diet Plans Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Condition Meal Plans
          </CardTitle>
          <CardDescription>
            Manage 7-day meal plans for different fertility conditions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No meal plans found</h3>
            <p className="text-muted-foreground mb-4">
              No condition-based meal plans have been created yet
            </p>
            <Link href="/admin/diet-plans/create">
              <Button
                type={ButtonType.PRIMARY}
                text="Create First Meal Plan"
                icon={<PlusCircle className="h-4 w-4" />}
              />
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
