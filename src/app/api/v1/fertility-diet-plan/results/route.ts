
import { calculateBMRForUser } from "@/lib/services/bmr.service";
import { authenticate } from "@/utils/api/authenticate";
import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";

export async function GET(req: NextRequest) {

  const { user, error } = await authenticate(req);
  if (error){
    return apiResponse(error.status, JSON.stringify(error));
  };

  if (!user) {
    return apiResponse(401, "Unauthorized");
  }

  // check current user record exist in `diet_plan_scores` table or not
  const { PrismaClient } = await import("@/generated/prisma");
  const prisma = new PrismaClient();
  const dietPlanScore = await prisma.diet_plan_scores.findUnique({
    where: { user_id: user.id },
  });
  if (!dietPlanScore) {
    return apiResponse(404, "Diet plan score not found for this user");
  }

  try {
    const bmrData = await calculateBMRForUser(user.id);
    const bmr = bmrData.score;
    const targetCalories = bmr + 300; // Assuming target calories is BMR + 300

    return apiResponse(200, "BMR calculated successfully", {
      bmr,
      targetCalories,
    });

  } catch (error) {
    console.error("Error calculating BMR:", error);
    return apiResponse(500, "Failed to calculate BMR");
  }
}
