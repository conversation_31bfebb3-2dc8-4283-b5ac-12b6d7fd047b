import React, { createContext, useContext, useState } from "react";

export type PageHeaderContextType = {
  title: string;
  subtitle: string;
  setTitle: (title: string) => void;
  setSubtitle: (subtitle: string) => void;
};

const PageHeaderContext = createContext<PageHeaderContextType | undefined>(
  undefined
);

export const usePageHeader = () => {
  const ctx = useContext(PageHeaderContext);
  if (!ctx)
    throw new Error("usePageHeader must be used within PageHeaderProvider");
  return ctx;
};

export const PageHeaderProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [title, setTitle] = useState("");
  const [subtitle, setSubtitle] = useState("");
  return (
    <PageHeaderContext.Provider
      value={{ title, subtitle, setTitle, setSubtitle }}
    >
      {children}
    </PageHeaderContext.Provider>
  );
};
