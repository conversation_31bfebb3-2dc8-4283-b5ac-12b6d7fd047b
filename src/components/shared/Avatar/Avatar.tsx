import React from "react";
import Image from "next/image";

interface AvatarProps {
  src?: string;
  alt?: string;
  width?: number;
  height?: number;
}

const Avatar: React.FC<AvatarProps> = ({
  src = "/assets/avatar.jpg",
  alt = "User",
  width = 48,
  height = 48,
}) => (
  <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white">
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className="rounded-full object-cover w-full h-full"
    />
  </div>
);

export default Avatar;
