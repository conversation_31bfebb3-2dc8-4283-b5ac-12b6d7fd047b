import { checkAdminRole } from './adminRoleCheck';
import { createClient } from '@/utils/supabase/client';

// Mock the Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(),
}));

describe('checkAdminRole', () => {
  const mockSupabase = {
    rpc: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it('should return true when user has admin role', async () => {
    mockSupabase.rpc.mockResolvedValue({
      data: true,
      error: null,
    });

    const result = await checkAdminRole();
    expect(result).toBe(true);
    expect(mockSupabase.rpc).toHaveBeenCalledWith('authorize', {
      resource_action: 'admin.access',
    });
  });

  it('should return false when user does not have admin role', async () => {
    mockSupabase.rpc.mockResolvedValue({
      data: false,
      error: null,
    });

    const result = await checkAdminRole();
    expect(result).toBe(false);
  });

  it('should return false when there is an error', async () => {
    mockSupabase.rpc.mockResolvedValue({
      data: null,
      error: { message: 'Database error' },
    });

    const result = await checkAdminRole();
    expect(result).toBe(false);
  });

  it('should return false when an exception is thrown', async () => {
    mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

    const result = await checkAdminRole();
    expect(result).toBe(false);
  });
}); 