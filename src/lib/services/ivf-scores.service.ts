"use server";

import { PrismaClient } from "@/generated/prisma";
import { redirect } from "next/navigation";
import { calculateDynamicIVFScore, IVFScoreResult } from "@/lib/services/ivf-scoring";
import { FieldValue } from "@/types/ivf-score/ivf-score";

const prisma = new PrismaClient();

export interface IvfScore {
  id: string;
  user_id: string;
  score: number;
  created_at: Date;
  updated_at: Date;
  user: {
    email: string | null;
    user_metadata: {
      first_name?: string;
      last_name?: string;
    }
  } | null;
}

export interface GetIvfScoresResult {
  scores: IvfScore[];
  total: number;
  error: string | null;
}

export async function getIvfScores(page: number = 1, search?: string, perPage: number = 10): Promise<GetIvfScoresResult> {
  try {
    const where = search ? {
      user: {
        email: {
          contains: search,
          mode: 'insensitive' as const
        }
      }
    } : {};

    const scores = await prisma.ivf_scores.findMany({
      where,
      take: perPage,
      skip: (page - 1) * perPage,
      include: {
        user: {
          select: {
            email: true
          }
        }
      }
    });

    const total = await prisma.ivf_scores.count({ where });

    return {
      scores: scores as unknown as IvfScore[],
      total,
      error: null,
    };
  } catch (error) {
    console.error('Error in getIvfScores:', error);
    return {
      scores: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteIvfScore(formData: FormData) {
  const scoreId = formData.get("scoreId") as string;
  
  try {
    await prisma.ivf_scores.delete({
      where: {
        id: scoreId
      }
    });
    
    redirect("/admin/ivf-scores");
  } catch (error) {
    console.error("Error deleting IVF score:", error);
    throw error;
  }
}

/**
 * Determine the status of an IVF assessment based on current_step and other criteria
 */
export async function determineIVFAssessmentStatus(currentStep: number, hasError: boolean = false): Promise<'pending' | 'completed' | 'failed'> {
  if (hasError) {
    return 'failed';
  }
  
  if (currentStep >= 3) {
    return 'completed';
  }
  
  return 'pending';
}

/**
 * Shared function to fetch IVF scores and calculate results for a user.
 * Returns { error } if not found or incomplete, else { scoreData, ivfScores }.
 */
export async function getUserIVFScoresAndCalculate({
  userId,
  debug = false,
  minStep = 3,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectUser = false
}: {
  userId: string;
  debug?: boolean;
  minStep?: number;
  selectUser?: boolean;
}): Promise<
  | { error: { status: number; message: string } }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | { scoreData: IVFScoreResult; ivfScores: any }
> {
  const ivfScores = await prisma.ivf_scores.findUnique({
    where: { user_id: userId },
    include: {
      user: {
        select: {
          display_name: true,
          email: true
        }
      }
    }
  });


  if (!ivfScores) {
    return { error: { status: 404, message: "IVF scores not found for this user" } };
  }
  if (ivfScores.current_step < minStep) {
    return { error: { status: 400, message: "All steps must be completed to view results" } };
  }

  const scoreData = await calculateDynamicIVFScore(
    {
      id: ivfScores.id,
      user_id: ivfScores.user_id,
      form_data: ivfScores.form_data as Record<string, FieldValue>,
      current_step: ivfScores.current_step,
      created_at: ivfScores.created_at,
      updated_at: ivfScores.updated_at,
      selected_track: ivfScores.selected_track ?? undefined
    },
    debug,
    ivfScores.selected_track ?? undefined,
    ['biological','lifestyle','environmental']
  );


  return { scoreData, ivfScores } as {
    scoreData: IVFScoreResult;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ivfScores: any;
  };
}
