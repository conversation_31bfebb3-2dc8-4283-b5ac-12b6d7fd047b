import { supabase } from "@/utils/supabase/client";
import { useQuery } from "@tanstack/react-query";

const getCurrentUserProfile = async () => {
    const profile = localStorage.getItem("ivf_user_profile");
    if (profile) {
        return JSON.parse(profile);
    }
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return null;
    }

    const { data, error } = await supabase
        .from('profiles')
        .select(`*`)
        .eq('auth_id', user.id)
        .single();

    if (error) {
        console.error('Error fetching profile:', error);
        throw error;
    }

    localStorage.setItem("ivf_user_profile", JSON.stringify(data));
    return data;
};

export const useUserProfile = () => {
    return useQuery({
        queryKey: ['user-profile'],
        queryFn: () => getCurrentUserProfile(),
    });
};
