import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import PageHeader from "./PageHeader";

const meta: Meta<typeof PageHeader> = {
  title: "Components/Shared/PageHeader",
  component: PageHeader,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The main title to display",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: "Create Your GIVF Account",
  },
};

export const VerifyAccount: Story = {
  args: {
    title: "Verify Your Account",
  },
};

export const CustomStyling: Story = {
  args: {
    title: "Custom Styled Header",
    className: "text-center mb-8",
  },
};

export const LongTitle: Story = {
  args: {
    title:
      "This is a Very Long Title That Might Wrap to Multiple Lines on Smaller Screens",
  },
};

export const ShortTitle: Story = {
  args: {
    title: "Login",
  },
};

export const WithSpecialCharacters: Story = {
  args: {
    title: "Welcome! Let&apos;s Get Started & Create Your Account",
  },
};

export const MobileView: Story = {
  args: {
    title: "Create Your GIVF Account",
  },
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {
    title: "Create Your GIVF Account",
  },
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};
