import { createClient } from "@/utils/supabase/client";

/**
 * Check if the current user has admin role
 * @returns Promise<boolean> - true if user has admin role, false otherwise
 */
export async function checkAdminRole(): Promise<boolean> {
  try {
    const supabase = createClient();
    const { data, error } = await supabase.rpc('authorize', {
      resource_action: 'admin.access'
    });
    
    if (error) {
      console.error('Error checking admin role:', error);
      return false;
    }
    
    return !!data;
  } catch (error) {
    console.error('Error checking admin role:', error);
    return false;
  }
} 