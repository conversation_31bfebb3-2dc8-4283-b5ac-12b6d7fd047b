import React from "react";
import SidebarItem from "../SidebarItem/SidebarItem";
import { Settings, LifeBuoy } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

export interface PopUpSidebarProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items: any[];
  isOpen: boolean;
  onClose: () => void;
  currentPathname: string;
  className?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  renderSidebarItems: (items: any[], isCollapsed: boolean) => React.ReactNode;
}

const isActive = (pathname: string, currentPathname: string) => {
  return currentPathname.includes(pathname);
};

const PopUpSidebar: React.FC<PopUpSidebarProps> = ({
  items,
  className,
  isOpen,
  currentPathname,
  onClose,
  renderSidebarItems,
}) => {
  const isCollapsed: boolean = false;
  const router = useRouter();
  return (
    <>
      <div
        className={`fixed inset-0 bg-black transition-opacity duration-600 z-40 ${isOpen ? "opacity-50" : "opacity-0 pointer-events-none"}`}
        onClick={onClose}
      />
      <div
        className={`${className}
        flex flex-col h-screen bg-white shadow-lg pt-6 pb-8 transform transition-transform duration-1000 ease-in-out z-50
        ${isOpen ? "translate-x-0" : "-translate-x-full"}
      `}
      >
        {/* Logo Section */}
        <div
          className={`flex items-center ${isCollapsed ? "hidden" : "justify-between gap-8"}`}
        >
          {!isCollapsed && (
            <div className="pl-[1.25rem]">
              <Image
                src="/assets/givfLogo.png"
                alt="GIVF Logo"
                width={120}
                height={40}
                className="object-contain"
              />
            </div>
          )}
          <button
            onClick={onClose}
            className="p-2 cursor-pointer rounded-sm"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            <Image
              src="/assets/menu.svg"
              alt="Toggle sidebar"
              width={24}
              height={24}
              className={`transition-transform duration-300 ${isCollapsed ? "rotate-180" : ""}`}
            />
          </button>
        </div>

        {/* Main Navigation Items */}
        <nav
          className={
            isCollapsed ? "hidden" : "flex-1 overflow-y-auto py-6 px-[1.25rem]"
          }
        >
          <div className="space-y-2">
            {renderSidebarItems(items, isCollapsed)}
          </div>
        </nav>

        {/* Fixed Bottom Items */}
        <div
          className={
            isCollapsed
              ? "hidden"
              : "px-[1.25rem] py-6 mt-auto border-t border-[var(--grey-3)]"
          }
        >
          <div className="space-y-2">
            <SidebarItem
              icon={<Settings />}
              title={isCollapsed ? "" : "Settings"}
              isSelected={isActive("/user/settings", currentPathname)}
              onClick={() => {
                router.push("/user/settings");
                onClose();
              }}
            />
            <SidebarItem
              icon={<LifeBuoy />}
              title={isCollapsed ? "" : "Help & Support"}
              isSelected={isActive("/user/help-support", currentPathname)}
              onClick={() => {
                router.push("/user/help-support");
                onClose();
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PopUpSidebar;