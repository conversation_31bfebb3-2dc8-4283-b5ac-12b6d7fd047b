import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  FileText,
  PlusCircle,
  Edit,
  Trash2,
  Search,
  Filter,
  Eye,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { getIvfScores, deleteIvfScore } from "@/lib/services/ivf-scores.service";
import { formatDate } from "@/lib/utils/user-management.utils";
import Link from "next/link";
import { getProfilesByUserIds } from "@/lib/services/user.service";


// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default async function IvfScoresPage({ searchParams }: any) {
  // Await searchParams if it's a Promise
  if (typeof searchParams?.then === "function") {
    searchParams = await searchParams;
  }

  const page = parseInt(searchParams?.page || "1");
  const search = searchParams?.search || "";
  const perPage = parseInt(searchParams?.per_page || "10");

  const { scores: ivfScores, total, error } = await getIvfScores(page, search, perPage);

  const userIds     = ivfScores.map((score) => score.user_id);
  const profiles    = await getProfilesByUserIds(userIds);
  const totalPages = Math.ceil(total / perPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">IVF Scores</h1>
            <p className="text-muted-foreground">
              Manage and view IVF assessment scores
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            type={ButtonType.SECONDARY}
            size="sm"
            text="Filter"
            icon={<Filter className="h-4 w-4" />}
            className="gap-2"
          />
          <Button
            type={ButtonType.PRIMARY}
            size="sm"
            text="Add Score"
            icon={<PlusCircle className="h-4 w-4" />}
            className="gap-2 whitespace-nowrap"
          />
        </div>
      </div>

      {/* IVF Scores Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                IVF Scores List
              </CardTitle>
              <CardDescription>
                {search
                  ? `Showing results for "${search}"`
                  : `Showing ${ivfScores.length} of ${total} scores`}
              </CardDescription>
            </div>

            {/* Search */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <form method="get" className="flex gap-2">
                  <input
                    type="text"
                    name="search"
                    placeholder="Search scores..."
                    defaultValue={search}
                    className="pl-10 pr-4 py-2 border rounded-md text-sm w-64"
                  />
                  <input type="hidden" name="page" value="1" />
                  <Button
                    type={ButtonType.SECONDARY}
                    size="sm"
                    text="Search"
                  />
                </form>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">Error loading scores</div>
              <div className="text-sm text-muted-foreground">{error}</div>
            </div>
          ) : ivfScores.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No IVF scores found</h3>
              <p className="text-muted-foreground mb-4">
                {search
                  ? "Try adjusting your search criteria"
                  : "No scores have been recorded yet"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Table */}
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Patient</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ivfScores.map((score) => {
                      const profile = profiles.find(p=> p.auth_id === score.user_id);
                      return (
                        <TableRow key={score.id}>
                          <TableCell>
                            <Link href={`/admin/ivf-scores/${score.user_id}`}>
                            <div className="font-medium">{profile?.display_name || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">{profile?.email || 'N/A'}</div>
                            </Link>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(score.created_at.toString())}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="View Details"
                                asChild
                              >
                                <Link href={`/admin/ivf-scores/${score.user_id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="Edit Score"
                                asChild
                              >
                                <Link href={`/admin/ivf-scores/${score.user_id}/edit`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <form action={deleteIvfScore} style={{ display: 'inline' }}>
                                <input type="hidden" name="scoreId" value={score.id} />
                                <ShadcnButton
                                  type="submit"
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                  title="Delete Score"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </ShadcnButton>
                              </form>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((page - 1) * perPage) + 1} to {Math.min(page * perPage, total)} of {total} scores
                  </div>
                  <div className="flex items-center gap-2">
                    <a href={`?page=${page - 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                      <Button
                        type={ButtonType.SECONDARY}
                        size="sm"
                        disabled={page <= 1}
                        text="Previous"
                        icon={<ChevronLeft className="h-4 w-4" />}
                      />
                    </a>
                    
                    <div className="flex items-center gap-1">
                      {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                        const pageNum = i + 1;
                        return (
                          <a key={pageNum} href={`?page=${pageNum}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                            <Button
                              type={page === pageNum ? ButtonType.PRIMARY : ButtonType.SECONDARY}
                              size="sm"
                              text={pageNum.toString()}
                            />
                          </a>
                        );
                      })}
                    </div>

                    <a href={`?page=${page + 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`}>
                      <Button
                        type={ButtonType.SECONDARY}
                        size="sm"
                        disabled={page >= totalPages}
                        text="Next"
                        icon={<ChevronRight className="h-4 w-4" />}
                      />
                    </a>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}