"use client";

import React from "react";
import { useRouter } from "next/navigation";
import <PERSON><PERSON>, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import <PERSON><PERSON>, { ButtonType } from "../shared/Button/Button";

// Icons
const AssessmentIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4" />
    <path d="M9 7V3a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v4" />
    <line x1="9" y1="11" x2="9" y2="17" />
    <line x1="15" y1="11" x2="15" y2="17" />
    <line x1="12" y1="11" x2="12" y2="17" />
  </svg>
);

const CalendarIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
    <line x1="16" y1="2" x2="16" y2="6" />
    <line x1="8" y1="2" x2="8" y2="6" />
    <line x1="3" y1="10" x2="21" y2="10" />
  </svg>
);

const TrendUpIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" />
    <polyline points="17,6 23,6 23,12" />
  </svg>
);

const UserIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

const PlayIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <polygon points="5,3 19,12 5,21" />
  </svg>
);

const EyeIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

interface DashboardProps {
  className?: string;
}

const Dashboard: React.FC<DashboardProps> = ({ className = "" }) => {
  const router = useRouter();

  // Mock user data - in real app this would come from props or state management
  const userData = {
    name: "Jyoti Mishra",
    email: "<EMAIL>",
    lastAssessment: "2 days ago",
    totalAssessments: 3,
    averageScore: 85.2,
    lastScore: 85.2,
    fertilityBand: "High Fertility",
  };

  // Mock recent assessments data
  const recentAssessments = [
    {
      id: 1,
      date: "Jan 15, 2025",
      score: 85.2,
      band: "High Fertility",
      status: "Completed",
    },
    {
      id: 2,
      date: "Dec 20, 2024",
      score: 78.5,
      band: "Moderate Fertility",
      status: "Completed",
    },
    {
      id: 3,
      date: "Nov 10, 2024",
      score: 72.1,
      band: "Moderate Fertility",
      status: "Completed",
    },
  ];

  const handleNewAssessment = () => {
    router.push("/ivf-assessment/user-type");
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleViewResults = (assessmentId?: number) => {
    router.push("/ivf-assessment/results");
  };

  const handleBookConsultation = () => {
    console.log("Navigate to book consultation");
  };

  const handleProfile = () => {
    console.log("Navigate to profile");
  };

  const getBandColor = (band: string) => {
    if (band.toLowerCase().includes("high")) {
      return "text-[var(--success-green-4)] bg-[var(--success-green-1)]";
    } else if (band.toLowerCase().includes("low")) {
      return "text-[var(--error-red-4)] bg-[var(--error-red-1)]";
    } else {
      return "text-[var(--warning-yellow-4)] bg-[var(--warning-yellow-1)]";
    }
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.HELP} />

      <main className="flex-1 py-6 px-4 md:px-[9.375rem]">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-[var(--grey-7)] text-3xl md:text-4xl font-bold mb-2">
              Welcome back, {userData.name}!
            </h1>
            <p className="text-[var(--grey-6)] text-lg">
              Track your fertility journey and access personalized insights
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[var(--violet-1)] rounded-full flex items-center justify-center mr-4">
                  <AssessmentIcon />
                </div>
                <div>
                  <p className="text-[var(--grey-5)] text-sm">
                    Total Assessments
                  </p>
                  <p className="text-[var(--grey-7)] text-2xl font-bold">
                    {userData.totalAssessments}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[var(--success-green-1)] rounded-full flex items-center justify-center mr-4">
                  <TrendUpIcon />
                </div>
                <div>
                  <p className="text-[var(--grey-5)] text-sm">Latest Score</p>
                  <p className="text-[var(--grey-7)] text-2xl font-bold">
                    {userData.lastScore}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[var(--orange-1)] rounded-full flex items-center justify-center mr-4">
                  <UserIcon />
                </div>
                <div>
                  <p className="text-[var(--grey-5)] text-sm">Fertility Band</p>
                  <p
                    className={`text-sm font-medium px-2 py-1 rounded ${getBandColor(userData.fertilityBand)}`}
                  >
                    {userData.fertilityBand}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[var(--blue-1)] rounded-full flex items-center justify-center mr-4">
                  <CalendarIcon />
                </div>
                <div>
                  <p className="text-[var(--grey-5)] text-sm">
                    Last Assessment
                  </p>
                  <p className="text-[var(--grey-7)] text-sm font-medium">
                    {userData.lastAssessment}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Recent Assessments */}
            <div className="lg:col-span-2">
              <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6">
                <h2 className="text-[var(--grey-7)] text-xl font-semibold mb-6">
                  Recent Assessments
                </h2>
                <div className="space-y-4">
                  {recentAssessments.map((assessment) => (
                    <div
                      key={assessment.id}
                      className="flex items-center justify-between p-4 border border-[var(--grey-2)] rounded-lg hover:bg-[var(--grey-1)] transition-colors duration-200"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-4">
                          <div>
                            <p className="text-[var(--grey-7)] font-medium">
                              Assessment #{assessment.id}
                            </p>
                            <p className="text-[var(--grey-5)] text-sm">
                              {assessment.date}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-[var(--grey-7)] font-semibold">
                              {assessment.score}%
                            </span>
                            <span
                              className={`text-xs px-2 py-1 rounded ${getBandColor(assessment.band)}`}
                            >
                              {assessment.band}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        type={ButtonType.SECONDARY}
                        text="View"
                        icon={<EyeIcon />}
                        onClick={() => handleViewResults(assessment.id)}
                        size="sm"
                        className="border border-[var(--grey-3)]"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6 mb-6">
                <h3 className="text-[var(--grey-7)] text-xl font-semibold mb-6">
                  Quick Actions
                </h3>
                <div className="space-y-4">
                  <Button
                    type={ButtonType.PRIMARY}
                    text="Take New Assessment"
                    icon={<PlayIcon />}
                    onClick={handleNewAssessment}
                    className="w-full"
                  />
                  <Button
                    type={ButtonType.SECONDARY}
                    text="Book Consultation"
                    onClick={handleBookConsultation}
                    className="w-full border border-[var(--grey-3)]"
                  />
                  <Button
                    type={ButtonType.SECONDARY}
                    text="View Profile"
                    onClick={handleProfile}
                    className="w-full border border-[var(--grey-3)]"
                  />
                </div>
              </div>

              {/* Progress Summary */}
              <div className="bg-[var(--grey-1)] rounded-lg p-6">
                <h3 className="text-[var(--grey-7)] text-lg font-semibold mb-4">
                  Your Progress
                </h3>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-[var(--grey-6)] text-sm">
                        Overall Score
                      </span>
                      <span className="text-[var(--grey-7)] text-sm font-medium">
                        {userData.averageScore}%
                      </span>
                    </div>
                    <div className="w-full h-2 bg-[var(--grey-3)] rounded-full">
                      <div
                        className="h-full bg-[var(--success-green-4)] rounded-full"
                        style={{ width: `${userData.averageScore}%` }}
                      ></div>
                    </div>
                  </div>
                  <p className="text-[var(--grey-6)] text-sm">
                    You&apos;re doing great! Keep tracking your progress with regular
                    assessments.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Dashboard;
