interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;

  constructor(maxAttempts: number = 3, windowMs: number = 24 * 60 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const entry = this.limits.get(identifier);

    if (!entry) {
      this.limits.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return true;
    }

    // Check if window has expired
    if (now > entry.resetTime) {
      this.limits.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return true;
    }

    // Check if limit exceeded
    if (entry.count >= this.maxAttempts) {
      return false;
    }

    // Increment count
    entry.count++;
    return true;
  }

  getRemainingAttempts(identifier: string): number {
    const entry = this.limits.get(identifier);
    if (!entry) return this.maxAttempts;
    
    const now = Date.now();
    if (now > entry.resetTime) return this.maxAttempts;
    
    return Math.max(0, this.maxAttempts - entry.count);
  }

  getResetTime(identifier: string): number | null {
    const entry = this.limits.get(identifier);
    return entry ? entry.resetTime : null;
  }

  // Clean up expired entries periodically
  cleanup(): void {
    const now = Date.now();
    for (const [identifier, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(identifier);
      }
    }
  }
}

// Create singleton instance
export const emailRateLimiter = new RateLimiter(3, 24 * 60 * 60 * 1000); // 3 attempts per day

// Clean up expired entries every hour
setInterval(() => {
  emailRateLimiter.cleanup();
}, 60 * 60 * 1000); 