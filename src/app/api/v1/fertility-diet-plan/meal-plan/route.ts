import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { calculateBMRForUser } from "@/lib/services/bmr.service";

const prisma = new PrismaClient();

// GET - Get personalized meal plan for user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user's diet plan score and calculate BMR
    const dietPlanScore = await prisma.diet_plan_scores.findUnique({
      where: { user_id: userId },
      include: {
        user: {
          select: {
            display_name: true,
            email: true
          }
        }
      }
    });

    if (!dietPlanScore) {
      return NextResponse.json(
        { success: false, error: 'Diet plan assessment not found. Please complete the assessment first.' },
        { status: 404 }
      );
    }

    // Calculate target calories from BMR
    const bmrData = await calculateBMRForUser(userId);
    const targetCalories = bmrData.score;

    // Determine condition from form data (this would need to be extracted from the assessment)
    // For now, we'll use a default condition
    const condition = 'PCOS'; // This should be extracted from the user's assessment

    // Get the appropriate meal plan based on condition and calories
    const mealPlan = await prisma.condition_meal_plans.findFirst({
      where: {
        condition,
        target_calories_min: { lte: targetCalories },
        target_calories_max: { gte: targetCalories },
        is_active: true
      },
      orderBy: { created_at: 'desc' }
    });

    if (!mealPlan) {
      return NextResponse.json(
        { success: false, error: 'No suitable meal plan found for your profile. Please contact our nutrition team.' },
        { status: 404 }
      );
    }

    // Extract week plan data
    const weekPlan: any = {};
    for (let day = 1; day <= 7; day++) {
      weekPlan[`day_${day}`] = {
        breakfast: mealPlan[`day_${day}_breakfast` as keyof typeof mealPlan] || { items: [], calories: 0 },
        lunch: mealPlan[`day_${day}_lunch` as keyof typeof mealPlan] || { items: [], calories: 0 },
        dinner: mealPlan[`day_${day}_dinner` as keyof typeof mealPlan] || { items: [], calories: 0 },
        snacks: mealPlan[`day_${day}_snacks` as keyof typeof mealPlan] || { items: [], calories: 0 }
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        user: dietPlanScore.user,
        bmr: bmrData.score,
        targetCalories,
        condition,
        nutritionalAdvice: mealPlan.nutritional_advice,
        weekPlan
      }
    });
  } catch (error) {
    console.error('Error fetching meal plan:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch meal plan' },
      { status: 500 }
    );
  }
}
