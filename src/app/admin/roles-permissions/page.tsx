import React from "react";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Shield, Users, Trash2 } from "lucide-react";
import { Badge } from "@/components/ShadcnUI/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import { QuickRoleForm, AdvancedRoleForm, EditRoleForm } from "./RoleDialogs";
import PermissionToggle from "@/components/admin/roles/PermissionToggle";
import {
  createRole,
  updateRole,
  deleteRole,
  fixRoleSequence,
  createRoleWithPermissions,
  getRolesWithPermissions,
} from "@/lib/services/roles-permissions.service";
import {
  hasPermission,
  AVAILABLE_RESOURCES,
  PERMISSION_ACTIONS,
} from "@/lib/utils/roles-permissions.utils";

export default async function RolesPermissionsPage() {
  const roles = await getRolesWithPermissions();
  
  const totalPermissions = roles.reduce((total, role) => total + role.permissions.length, 0);
  const totalRoles = roles.length;
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Roles & Permissions</h1>
            <p className="text-muted-foreground">
              Manage user roles and their permissions across the system
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <form action={fixRoleSequence} style={{ display: 'inline' }}>
            <Button type="submit" variant="outline" size="sm" className="gap-2 text-xs">
              🔧 Fix Sequence
            </Button>
          </form>
          <AdvancedRoleForm createRoleWithPermissionsAction={createRoleWithPermissions} />
          <QuickRoleForm createRoleAction={createRole} />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Total Roles</span>
            </div>
            <div className="text-2xl font-bold">{totalRoles}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Total Permissions</span>
            </div>
            <div className="text-2xl font-bold">{totalPermissions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Resources</span>
            </div>
            <div className="text-2xl font-bold">{AVAILABLE_RESOURCES.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Actions</span>
            </div>
            <div className="text-2xl font-bold">4</div>
          </CardContent>
        </Card>
      </div>

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Roles Management
              </CardTitle>
              <CardDescription>
                Create, edit, and manage user roles and their associated permissions
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {roles.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No roles found</h3>
              <p className="text-muted-foreground mb-4">
                Get started by creating your first role
              </p>
              <QuickRoleForm createRoleAction={createRole} />
            </div>
          ) : (
            <div className="space-y-4">
              {/* Table */}
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">Role</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id.toString()}>
                        <TableCell>
                          <div className="font-medium">{role.name}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-muted-foreground">
                            {role.description || 'No description'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {role.permissions.map((permission) => (
                              <Badge key={permission.id.toString()} variant="secondary" className="text-xs">
                                {permission.resource}
                              </Badge>
                            ))}
                            {role.permissions.length === 0 && (
                              <span className="text-xs text-muted-foreground">No permissions</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {new Date(role.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <EditRoleForm role={role} updateRoleAction={updateRole} />
                            <form action={deleteRole} style={{ display: 'inline' }}>
                              <input type="hidden" name="id" value={role.id.toString()} />
                              <Button 
                                type="submit" 
                                size="sm" 
                                variant="ghost" 
                                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                title="Delete Role"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </form>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Interactive Permissions Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Permissions Matrix</CardTitle>
          <CardDescription>
            Click buttons to grant or revoke permissions for each role across different resources
          </CardDescription>
        </CardHeader>
        <CardContent>
          {roles.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Create roles to manage permissions</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">Resource</TableHead>
                    {roles.map((role) => (
                      <TableHead key={role.id.toString()} className="text-center min-w-[160px]">
                        <div className="flex flex-col items-center gap-1">
                          <span className="font-medium">{role.name}</span>
                          <div className="flex gap-1 text-xs text-muted-foreground">
                            <span>C</span>
                            <span>R</span>
                            <span>U</span>
                            <span>D</span>
                          </div>
                        </div>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {AVAILABLE_RESOURCES.map((resource) => (
                    <TableRow key={resource}>
                      <TableCell className="font-medium capitalize">
                        {resource.replace('_', ' ')}
                      </TableCell>
                      {roles.map((role) => (
                        <TableCell key={`${role.id}-${resource}`} className="text-center">
                          <div className="flex justify-center gap-1">
                            {PERMISSION_ACTIONS.map((action) => (
                              <PermissionToggle
                                key={action}
                                role={role}
                                resource={resource}
                                action={action}
                                hasPermission={hasPermission(role, resource, action)}
                              />
                            ))}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
