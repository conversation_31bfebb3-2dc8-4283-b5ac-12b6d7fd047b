import React, { useState } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import Input from "../shared/Input/Input";
import { Mail } from "lucide-react";
import Image from "next/image";
import PasswordInput from "../shared/PasswordInput";

export interface LoginPageProps {
  onLogin?: (email: string, password: string) => void;
  onLoginWithOTP?: () => void;
  onContinueWithGoogle?: () => void;
  onContinueWithEmail?: () => void;
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  onGetHelp?: () => void;
}

const LoginPage: React.FC<LoginPageProps> = ({
  onLogin,
  onLoginWithOTP,
  onContinueWithGoogle,
  onContinueWithEmail,
  onForgotPassword,
  onGetHelp,
}) => {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const [rememberMe, setRememberMe] = useState(true);

  const handleLogin = () => {
    if (onLogin) {
      onLogin(email, password);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <Header state={HeaderState.HELP} onClick={onGetHelp} />

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-6 py-16">
        <div className="w-full flex justify-center">
          {/* Login Form */}
          <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-12">
            {/* Title */}
            <div className="text-center flex flex-col">
              <h1 className="text-center pt-[0.563rem] text-2xl font-semibold text-[var(--grey-7)] mb-2">
                Welcome Back
                <div className="flex justify-center py-1">
                  <Image
                    src="/assets/loginPage/Line.png"
                    alt="Decorative line"
                    className="h-1 w-16"
                    width={100}
                    height={9}
                  />
                </div>
              </h1>

              <p className="text-[var(--grey-6)] text-base font-medium">
                Enter your credentials to log in and access your
                <br />
                personalized IVF dashboard.
              </p>
            </div>

            {/* Form */}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleLogin();
              }}
            >
              <div className="space-y-4">
                {/* Email Field */}
                <div className="flex flex-col gap-2">
                  <Input
                    type="email"
                    label="Email or Number"
                    value={email}
                    onChange={setEmail}
                    placeholder="Enter your email address"
                    maxLength={50}
                  />
                </div>

                {/* Password Field */}
                <div>
                  <label
                    htmlFor="password"
                    className="block text-base font-medium text-[var(--grey-6)] mb-2"
                  >
                    Password
                  </label>
                  <PasswordInput
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                  />
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="remember-me"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className={`w-4 h-4 text-[var(--red-6)] border-gray-300 rounded ${rememberMe && "bg-[var(--violet-6)]"}`}
                    />
                    <label
                      htmlFor="remember-me"
                      className="ml-2 text-base font-medium text-[var(--grey-6)]"
                    >
                      Remember me
                    </label>
                  </div>
                  <button
                    type="button"
                    onClick={onForgotPassword}
                    className="cursor-pointer text-base font-medium underline text-[var(--red-6)] hover:text-[var(--red-7)] transition-colors duration-200"
                  >
                    Forgot Password?
                  </button>
                </div>

                {/* Login Button */}
                <div className="pt-2">
                  <Button
                    type={ButtonType.PRIMARY}
                    text="Login"
                    onClick={handleLogin}
                  />
                </div>

                {/* Login with OTP */}
                <div>
                  <Button
                    type={ButtonType.SECONDARY}
                    text="Login with OTP"
                    onClick={onLoginWithOTP || (() => {})}
                  />
                </div>
              </div>
            </form>

            {/* Divider */}
            <div>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-[var(--grey-3)]"></div>
                </div>
                <div className="relative flex justify-center text-base font-medium">
                  <span className="px-2 bg-white text-[var(--grey-5)]">
                    Don&apos;t have an account?
                  </span>
                </div>
              </div>
            </div>

            {/* Social Login Buttons */}
            <div className="space-y-3 flex flex-col gap-3">
              <button
                type="button"
                onClick={onContinueWithGoogle}
                className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] hover:bg-gray-50 transition-colors duration-200 shadow-[0_4px_8px_rgba(0,0,0,0.05)]"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Continue with Google
              </button>

              <button
                type="button"
                onClick={onContinueWithEmail}
                className="w-full flex items-center justify-center gap-3 px-4 py-3 border border-[var(--grey-3)] rounded-sm text-[var(--grey-7)] hover:bg-gray-50 transition-colors duration-200"
              >
                <Mail size={20} />
                Continue with Email
              </button>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default LoginPage;
