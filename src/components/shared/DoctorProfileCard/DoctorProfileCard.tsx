import Image from "next/image";
import React from "react";
import {
  BriefcaseIcon,
  MapPinSimpleIcon,
  UserCircleIcon,
} from "@phosphor-icons/react";
import { useScreenWidth } from "@/hooks/useScreenWidth";

interface DoctorProfileCardProps {
  imageUrl: string;
  name: string;
  specialization: string;
  experience: string;
  clinicLocation: string;
  consultationType: string;
  bio: string;
  className?: string;
}

const DoctorProfileCard: React.FC<DoctorProfileCardProps> = ({
  imageUrl,
  name,
  specialization,
  experience,
  clinicLocation,
  consultationType,
  bio,
  className,
}) => {
  const screenWidth = useScreenWidth();
  return (
    <div
      className={`bg-white border-1 border-[var(--grey-3)] flex flex-col items-start gap-8 rounded-[8px] w-[362px] md:w-[434px] px-5 py-7.5 md:px-10 md:py-7.5 ${className}`}
    >
      <div className="flex flex-col gap-4">
        {/* Profile Image */}
        <Image
          src={imageUrl}
          alt={name}
          width={screenWidth < 768 ? 144 : 194}
          height={screenWidth < 768 ? 144 : 194}
          className="rounded-full object-cover"
        />

        {/* Doctor Info */}
        <div className="flex flex-col gap-4">
          <h3 className="text-xl font-bold text-[var(--grey-7)]">{name}</h3>
          <p className="text-[var(--grey-6)] text-base">{specialization}</p>

          {/* Experience */}
          <div className="flex justify-start text-base items-center text-[var(--grey-6)] gap-2">
            <BriefcaseIcon
              size={16}
              className="!text-[var(--violet-11)]"
              weight="bold"
            />
            {experience}
          </div>

          {/* Clinic Location */}
          <div className="flex justify-start text-base items-center text-[var(--grey-6)] gap-2">
            <MapPinSimpleIcon
              size={20}
              className="!text-[var(--violet-11)]"
              weight="bold"
            />
            {clinicLocation}
          </div>

          {/* Consultation Type */}
          <div className="flex justify-start items-center gap-2">
            <div className="inline-flex items-center gap-2 bg-[var(--violet-1)] text-[var(--violet-11)] px-4 py-2 rounded-full text-base font-medium">
              <UserCircleIcon size={16} weight="bold" />
              {consultationType}
            </div>
          </div>
        </div>
      </div>

      {/* Bio Section */}
      <div className="flex flex-col gap-[9px]">
        <h4 className="font-bold text-[var(--grey-7)] text-xl">Bio</h4>
        <p className="text-[var(--grey-6)] text-base font-medium leading-relaxed">
          {bio}
        </p>
      </div>
    </div>
  );
};

export default DoctorProfileCard;
