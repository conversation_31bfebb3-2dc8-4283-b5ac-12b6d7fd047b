import React from "react";
import { Question } from "@/types/questions";
import Input from "@/components/shared/Input/Input";
import ProgressBar from "@/components/shared/ProgressBar/ProgressBar";
import ErrorMessage from "@/components/shared/ErrorMessage/ErrorMessage";
import Dropdown, {
  DropdownOption,
} from "@/components/shared/Dropdown/Dropdown";
import RadioSelect, {
  RadioOption,
} from "@/components/shared/RadioSelect/RadioSelect";

interface DynamicFormFieldProps {
  question: Question;
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  errorMessage?: string;
  required?: boolean;
}

const DynamicFormField: React.FC<DynamicFormFieldProps> = ({
  question,
  value,
  onChange,
  error = false,
  errorMessage,
  required = false,
}) => {
  const { field_type, question_text, config, options } = question;

  // Render different field types based on field_type.value
  switch (field_type.value) {
    case "NUMBER_INPUT":
      return (
        <div className="relative">
          <Input
            type="number"
            required={required}
            label={question_text}
            value={value}
            onChange={onChange}
            placeholder={config.placeholder || ""}
            min={config.min_value}
            max={config.max_value}
            className="pr-12"
            error={error}
            errorMessage={errorMessage}
          />
          {config.unit && (
            <span className="absolute right-4 top-[3.5rem] transform -translate-y-1/2 text-[var(--grey-6)] text-base font-medium pointer-events-none">
              {config.unit}
            </span>
          )}
        </div>
      );

    case "RANGE_SLIDER":
      return (
        <div>
          <ProgressBar
            label={question_text}
            valueType="number"
            required={required}
            startValue={config.min_value || 0}
            endValue={config.max_value || 10}
            currentValue={value ? parseInt(value) : config.min_value || 0}
            min={config.min_value || 0}
            max={config.max_value || 10}
            onChange={(val) => onChange(val.toString())}
          />
          {error && errorMessage && (
            <ErrorMessage
              message={errorMessage}
              centered={false}
              className="mt-2"
            />
          )}
        </div>
      );

    case "RADIO_SELECT":
      const radioOptions: RadioOption[] = options.map((option) => ({
        id: option.id,
        text: option.text,
        value: option.text,
      }));

      return (
        <RadioSelect
          label={question_text}
          required={required}
          value={value}
          onChange={onChange}
          options={radioOptions}
          error={error}
          errorMessage={errorMessage}
          variant="compact"
        />
      );

    case "DROPDOWN_SELECT":
      const dropdownOptions: DropdownOption[] = options.map((option) => ({
        id: option.id,
        text: option.text,
        value: option.value ? option.value : option.text,
      }));

      return (
        <Dropdown
          label={question_text}
          required={required}
          value={value}
          onChange={onChange}
          options={dropdownOptions}
          placeholder="Select an option"
          error={error}
          errorMessage={errorMessage}
        />
      );

    case "INPUT":
      return (
        <Input
          type="text"
          label={question_text}
          required={required}
          value={value}
          onChange={onChange}
          placeholder={config.placeholder || ""}
          error={error}
          errorMessage={errorMessage}
        />
      );

    case "GROUP_QUESTION":
      return (
        <div className="pt-4">
          <h3 className="text-lg font-semibold text-[var(--grey-8)]">
            {question_text}
          </h3>
        </div>
      );

    default:
      return (
        <div className="text-[var(--error-red-4)]">
          Unsupported field type: {field_type.value}
        </div>
      );
  }
};

export default DynamicFormField;