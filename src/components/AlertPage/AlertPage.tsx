"use client";
import React from "react";
import Image from "next/image";
import Header from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import NeedHelp from "../shared/NeedHelp/NeedHelp";

export interface AlertPageProps {
  onRetakeAssessment?: () => void;
  onGoToResults?: () => void;
  onLogin?: () => void;
}

const AlertPage: React.FC<AlertPageProps> = ({
  onRetakeAssessment,
  onGoToResults,
  onLogin,
}) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <Header onClick={onLogin} />

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-6 md:px-[9.375rem] py-16">
        <div className="w-full max-w-4xl text-center flex flex-col items-center">
          {/* Hero Image */}
          <div className="mb-12 flex justify-center">
            {/* Main image */}
            <div className="relative mx-auto">
              <Image
                src="/assets/modalLogo.svg"
                alt="Response Submitted"
                width={350}
                height={350}
                className="w-full h-full"
                priority
              />
            </div>
          </div>

          {/* Content */}
          <div className="w-[22.125rem] md:w-[39.125rem] space-y-4">
            <p className="text-[1.5rem] md:text-[1.75rem] font-bold text-[var(--grey-7)]">
              Your Response Has Already Been Submitted
            </p>

            <p className="text-base md:text-xl text-[var(--violet-12)]">
              We have received your response. Would you like to retake the test
              or go to your results?
            </p>

            {/* CTA Button */}
            <div className="pt-6 flex gap-4 justify-center">
              <div className="max-w-xs">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Retake Assessment"
                  size="sm"
                  onClick={onRetakeAssessment}
                />
              </div>
              <div className="max-w-xs">
                <Button
                  type={ButtonType.SECONDARY}
                  text="Go Back to Results"
                  size="sm"
                  onClick={onGoToResults}
                />
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default AlertPage;
