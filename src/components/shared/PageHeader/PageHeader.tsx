import React from "react";
import Image from "next/image";

export interface PageHeaderProps {
  title: string;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, className = "" }) => {
  return (
    <h1
      className={`text-[var(--grey-7)] text-2xl md:text-[1.75rem] font-bold mb-3 ${className}`}
    >
      {title}
      <div className="flex justify-center py-1">
        <Image
          src="/assets/loginPage/Line.png"
          alt="Decorative line"
          width={64}
          height={4}
          className="h-1 w-16"
        />
      </div>
    </h1>
  );
};

export default PageHeader;
