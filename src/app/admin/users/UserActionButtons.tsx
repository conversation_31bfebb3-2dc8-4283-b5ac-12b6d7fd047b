// src/components/admin/UserActionButtons.tsx
"use client";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { Filter, UserPlus } from "lucide-react";

export default function UserActionButtons() {
  return (
    <div className="flex items-center gap-3">
      <Button
        type={ButtonType.SECONDARY}
        size="sm"
        text="Filter"
        icon={<Filter className="h-4 w-4" />}
        className="rounded whitespace-nowrap px-4 py-3 text-base font-semibold"
        onClick={() => {}}
      />
      <Button
        type={ButtonType.PRIMARY}
        size="sm"
        text="Add User"
        icon={<UserPlus className="h-4 w-4" />}
        className="rounded whitespace-nowrap px-4 py-3 text-base font-semibold"
        onClick={() => {}}
      />
    </div>
  );
}