"use server";

import { getServiceRoleSupabase } from "@/utils/supabase/service-role";
import { redirect } from "next/navigation";
import { PER_PAGE } from "@/lib/utils/user-management.utils";



// Types and Interfaces
export interface User {
  id: string;
  email?: string;
  user_metadata: {
    full_name?: string;
    first_name?: string;
    last_name?: string;
  };
  app_metadata: {
    provider?: string;
    providers?: string[];
  };
  created_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  role?: string;
}

export interface UsersPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    per_page?: string;
  }>;
}

export interface GetUsersResult {
  users: User[];
  total: number;
  error: string | null;
}

// Server Actions for User Management
export async function deleteUser(formData: FormData) {
  const userId = formData.get("userId") as string;
  
  try {
    const supabase = getServiceRoleSupabase();
    
    const { error } = await supabase.auth.admin.deleteUser(userId);
    
    if (error) {
      throw error;
    }
    
    // Redirect to refresh the page
    redirect("/admin/users");
  } catch (error) {
    console.error("Error deleting user:", error);
    // Handle error appropriately
    throw error;
  }
}

export async function updateUserName(formData: FormData) {
  const userId = formData.get("userId") as string;
  const fullName = formData.get("fullName") as string;
  
  try {
    const supabase = getServiceRoleSupabase();
    
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      user_metadata: {
        full_name: fullName
      }
    });
    
    if (error) {
      throw error;
    }
    
    // Redirect to refresh the page
    redirect("/admin/users");
  } catch (error) {
    console.error("Error updating user:", error);
    // Handle error appropriately
    throw error;
  }
}

// Data fetching function
export async function getUsers(page: number = 1, search?: string, perPage: number = PER_PAGE): Promise<GetUsersResult> {
  try {
    const supabase = getServiceRoleSupabase();
    
    // Get users using the admin API
    const { data: usersData, error: usersError } = await supabase.auth.admin.listUsers({
      page,
      perPage,
    });

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return { users: [], total: 0, error: usersError.message };
    }

    // Filter users by search if provided
    let filteredUsers = usersData.users || [];
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.email?.toLowerCase().includes(searchLower) ||
        user.user_metadata?.full_name?.toLowerCase().includes(searchLower) ||
        user.user_metadata?.first_name?.toLowerCase().includes(searchLower) ||
        user.user_metadata?.last_name?.toLowerCase().includes(searchLower)
      );
    }

    return {
      users: filteredUsers as User[],
      total: usersData.total || 0,
      error: null
    };
  } catch (error) {
    console.error('Error in getUsers:', error);
    return { 
      users: [], 
      total: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

 