import React from "react";

export interface DropdownOption {
  id: string;
  text: string;
  value: string;
}

export interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: DropdownOption[];
  placeholder?: string;
  label?: string;
  id?: string;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  variant?: "default" | "compact";
  size?: "sm" | "default" | "lg";
}

const Dropdown: React.FC<DropdownProps> = ({
  value,
  onChange,
  options,
  placeholder = "Select an option",
  label,
  id,
  disabled = false,
  required = false,
  error = false,
  errorMessage,
  className = "",
  variant = "default",
  size = "default",
}) => {
  const selectId = id || `dropdown-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses =
    "w-full px-4 py-3 border rounded-sm text-[var(--grey-7)] text-base bg-white focus:outline-none focus:ring-[1/2] transition-all duration-200 appearance-none";

  const variantClasses = {
    default:
      "border-[var(--grey-3)] focus:ring-[var(--violet-11)] focus:border-[var(--violet-11)]",
    compact:
      "border-[var(--grey-2)] focus:ring-[var(--violet-11)] focus:border-[var(--violet-11)]",
  };

  const sizeClasses = {
    sm: "h-10 text-sm",
    default: "h-[3.125rem] text-base font-bold",
    lg: "h-14 text-lg",
  };

  const stateClasses = error
    ? "border-red-300 focus:ring-red-500 focus:border-red-500"
    : variantClasses[variant];

  const disabledClasses = disabled
    ? "opacity-50 cursor-not-allowed bg-[var(--grey-1)]"
    : "";

  const combinedClasses = `${baseClasses} ${sizeClasses[size]} ${stateClasses} ${disabledClasses} ${className}`;

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  return (
    <>
      <div className="w-full">
        {label && (
          <label
            htmlFor={selectId}
            className="block text-[var(--grey-6)] text-base font-medium mb-2"
          >
            {label}
            {required && (
              <span className="text-[var(--error-red-4)] ml-1">*</span>
            )}
          </label>
        )}

        <div className="relative">
          <select
            id={selectId}
            value={value}
            onChange={handleChange}
            disabled={disabled}
            required={required}
            className={combinedClasses}
            aria-invalid={error}
            aria-describedby={
              error && errorMessage ? `${selectId}-error` : undefined
            }
          >
            <option value="">{placeholder}</option>
            {options.map((option) => (
              <option key={option.id} value={option.value}>
                {option.text}
              </option>
            ))}
          </select>

          {/* Custom dropdown arrow */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
            <svg
              className="w-4 h-4 text-[var(--grey-6)]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
          </div>
        </div>

        {error && errorMessage && (
          <p
            id={`${selectId}-error`}
            className="mt-1 text-sm text-[var(--error-red-4)]"
            role="alert"
          >
            {errorMessage}
          </p>
        )}
      </div>
    </>
  );
};

export default Dropdown;
