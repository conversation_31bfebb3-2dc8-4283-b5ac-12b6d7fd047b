import Image from "next/image";
import React from "react";
import { CalendarCheckIcon, ClockIcon } from "@phosphor-icons/react";

interface AppointmentCardProps {
  doctorImageUrl: string;
  doctorName: string;
  clinicName: string;
  clinicLocation: string;
  appointmentDate: string;
  appointmentTime: string;
  onReschedule: () => void;
  onCancel: () => void;
  className?: string;
}

const AppointmentCard: React.FC<AppointmentCardProps> = ({
  doctorImageUrl,
  doctorName,
  clinicName,
  clinicLocation,
  appointmentDate,
  appointmentTime,
  onReschedule,
  onCancel,
  className,
}) => {
  return (
    <div
      className={`bg-white rounded-[8px] border-1 border-[var(--grey-3)] px-7.5 py-5 w-[362px] md:w-[423px] flex flex-col gap-4 ${className}`}
    >
      {/* Doctor Info Section */}
      <div className="w-full flex items-center gap-3">
        <Image
          src={doctorImageUrl}
          alt={doctorName}
          width={48}
          height={48}
          className="rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="font-bold text-[var(--grey-7)] text-lg md:text-xl">
            {doctorName}
          </h3>
          <p className="text-[var(--grey-6)] text-sm md:text-base font-medium">
            {clinicName}, {clinicLocation}
          </p>
        </div>
      </div>

      {/* Appointment Details */}
      <div className="flex items-center justify-start gap-3">
        <div className="flex items-center gap-2">
          <CalendarCheckIcon
            size={16}
            weight="bold"
            className="text-[var(--violet-11)]"
          />
          <span className="text-[var(--grey-7)] text-base font-medium">
            {appointmentDate}
          </span>
        </div>
        <div className="w-px h-4 bg-[var(--grey-3)]"></div>
        <div className="flex items-center gap-2">
          <ClockIcon
            size={16}
            weight="bold"
            className="text-[var(--violet-11)]"
          />
          <span className="text-[var(--grey-7)] text-base font-medium">
            {appointmentTime}
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-around">
        <button
          onClick={onReschedule}
          className="text-[var(--primary-pink)] text-base font-medium cursor-pointer"
        >
          Reschedule
        </button>
        <button
          onClick={onCancel}
          className="text-[var(--grey-6)] text-sm font-medium cursor-pointer"
        >
          Cancel Appointment
        </button>
      </div>
    </div>
  );
};

export default AppointmentCard;
