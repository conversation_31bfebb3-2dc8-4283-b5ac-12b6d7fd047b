import React from "react";

export interface ProfileMenuItemProps {
  icon: React.ReactNode;
  text: string;
  onClick?: () => void;
  variant?: "secondary" | "primary";
  className?: string;
}

const ProfileMenuItem: React.FC<ProfileMenuItemProps> = ({
  icon,
  text,
  onClick,
  variant = "secondary",
  className = "",
}) => {
  const baseClasses =
    "w-full flex items-center gap-4 text-left transition-colors duration-200 py-1.5";

  const variantClasses = {
    secondary: "hover:bg-[var(--grey-1)] text-[var(--grey-7)]",
    primary: "hover:bg-[var(--red-1)] text-[var(--primary-pink)]",
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${className}`;

  return (
    <button onClick={onClick} className={buttonClasses}>
      <div className="w-6 h-6 flex items-center justify-center">{icon}</div>
      <span className="text-base">{text}</span>
    </button>
  );
};

export default ProfileMenuItem;
