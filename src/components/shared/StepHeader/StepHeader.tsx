import Image from "next/image";
import React from "react";

export interface StepHeaderProps {
  currentStep: number;
  totalSteps: number;
  title: string;
  className?: string;
}

const StepHeader: React.FC<StepHeaderProps> = ({
  currentStep,
  totalSteps,
  title,
  className = "",
}) => {
  return (
    <div className={`text-center flex flex-col gap-4 mb-10 ${className}`}>
      <p className="text-[var(--violet-6)] text-sm font-medium">
        Step <span className="text-[var(--grey-6)]">{currentStep}</span> of{" "}
        <span className="text-[var(--grey-6)]">{totalSteps}</span>
      </p>
      <h1 className="text-[var(--grey-7)] text-center text-[1.75rem] font-bold">
        {title}
        <div className="flex justify-center py-1">
          <Image
            src="/assets/loginPage/Line.png"
            alt="Decorative line"
            className="h-1 w-16"
            width={100}
            height={9}
          />
        </div>
      </h1>
    </div>
  );
};

export default StepHeader;
