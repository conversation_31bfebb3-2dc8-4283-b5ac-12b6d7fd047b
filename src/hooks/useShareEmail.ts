import { useState } from 'react';
import { toPng } from 'html-to-image';
import { getGuestSessionToken } from '@/utils/guestSessionUtils';
import { useAuth } from '@/contexts/AuthContext';

interface ShareEmailState {
  isCapturing: boolean;
  isSending: boolean;
  error: string | null;
  success: boolean;
}

export const useShareEmail = () => {
  const [state, setState] = useState<ShareEmailState>({
    isCapturing: false,
    isSending: false,
    error: null,
    success: false,
  });

  const { user, user_token } = useAuth();

  const captureAndShareEmail = async () => {
    try {
      setState(prev => ({ ...prev, isCapturing: true, error: null, success: false }));

      // Find the score results container
      const scoreResultsElement = document.getElementById('fertility-score-results');
      if (!scoreResultsElement) {
        throw new Error('Score results container not found');
      }

      // Capture the element as PNG
      const dataUrl = await toPng(scoreResultsElement, {
        quality: 0.95,
        backgroundColor: '#ffffff',
        style: {
          transform: 'scale(1)',
          transformOrigin: 'top left',
        },
      });

      setState(prev => ({ ...prev, isCapturing: false, isSending: true }));

      // Prepare request payload
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const payload: any = {
        imageBase64: dataUrl,
      };

      // Add guest session token if user is not authenticated
      if (!user) {
        const guestToken = getGuestSessionToken();
        if (guestToken) {
          payload.guestSessionToken = guestToken;
        }
      }

      // Send to API
      const response = await fetch('/api/share-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user_token && { Authorization: `Bearer ${user_token}` }),
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to send email');
      }

      setState(prev => ({ 
        ...prev, 
        isSending: false, 
        success: true,
        error: null 
      }));

      // Reset success state after 3 seconds
      setTimeout(() => {
        setState(prev => ({ ...prev, success: false }));
      }, 3000);

    } catch (error) {
      console.error('Share email error:', error);
      setState(prev => ({ 
        ...prev, 
        isCapturing: false, 
        isSending: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        success: false 
      }));
    }
  };

  const resetState = () => {
    setState({
      isCapturing: false,
      isSending: false,
      error: null,
      success: false,
    });
  };

  return {
    ...state,
    captureAndShareEmail,
    resetState,
  };
}; 