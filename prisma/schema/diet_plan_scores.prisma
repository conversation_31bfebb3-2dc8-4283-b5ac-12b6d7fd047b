model diet_plan_scores {
  id                          String   @id @default(uuid())
  user_id                     String   @unique @db.Uuid
  user                        profiles @relation(fields: [user_id], references: [auth_id], onDelete: Cascade, onUpdate: NoAction)

  // Dynamic data storage
  form_data                   Json     @db.Json // Stores all form responses as JSON
  
  // Track user's current step in the diet plan process
  current_step                Int      @default(1) // 1: Step 1 (diet-plan)

  // Status tracking
  status                      diet_plan_status @default(pending)

  created_at                  DateTime @default(now())
  updated_at                  DateTime @updatedAt
  
  diet_plans                  diet_plans[]
}

enum diet_plan_status {
  pending
  completed
  failed
} 