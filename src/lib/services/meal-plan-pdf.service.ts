"use server";

import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export interface MealPlanData {
  condition: string;
  targetCalories: number;
  nutritionalAdvice?: string;
  weekPlan: {
    [day: string]: {
      breakfast: { items: string[]; calories: number };
      lunch: { items: string[]; calories: number };
      dinner: { items: string[]; calories: number };
      snacks: { items: string[]; calories: number };
    };
  };
}

export async function generateMealPlanPDF(userId: string): Promise<string | null> {
  try {
    // Fetch meal plan data from API
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/v1/fertility-diet-plan/meal-plan?userId=${userId}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch meal plan data');
    }

    const { data } = await response.json();

    // Generate PDF content
    const pdfContent = generatePDFContent({
      userName: data.user?.display_name || "User",
      condition: data.condition,
      targetCalories: data.targetCalories,
      nutritionalAdvice: data.nutritionalAdvice,
      weekPlan: data.weekPlan
    });

    return pdfContent;
  } catch (error) {
    console.error('Error generating meal plan PDF:', error);
    return null;
  }
}

function extractWeekPlan(mealPlan: any) {
  const weekPlan: any = {};
  
  for (let day = 1; day <= 7; day++) {
    weekPlan[`day_${day}`] = {
      breakfast: mealPlan[`day_${day}_breakfast`] || { items: [], calories: 0 },
      lunch: mealPlan[`day_${day}_lunch`] || { items: [], calories: 0 },
      dinner: mealPlan[`day_${day}_dinner`] || { items: [], calories: 0 },
      snacks: mealPlan[`day_${day}_snacks`] || { items: [], calories: 0 }
    };
  }
  
  return weekPlan;
}

function generatePDFContent(data: {
  userName: string;
  condition: string;
  targetCalories: number;
  nutritionalAdvice?: string;
  weekPlan: any;
}): string {
  const getConditionDisplayName = (condition: string) => {
    switch (condition) {
      case 'PCOS': return 'PCOS';
      case 'LOW_AMH': return 'Low AMH';
      case 'INCREASE_FERTILITY_NATURALLY': return 'Increase Fertility Naturally';
      case 'INCREASE_IRON': return 'Increase Iron';
      default: return condition;
    }
  };

  let content = `
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .day { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; }
        .meal { margin-bottom: 15px; }
        .meal-title { font-weight: bold; color: #333; }
        .food-items { margin-left: 20px; }
        .calories { color: #666; font-size: 0.9em; }
        .advice { background: #f5f5f5; padding: 15px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Personalized 7-Day Meal Plan</h1>
        <p><strong>Patient:</strong> ${data.userName}</p>
        <p><strong>Condition:</strong> ${getConditionDisplayName(data.condition)}</p>
        <p><strong>Target Calories:</strong> ${data.targetCalories} per day</p>
      </div>
  `;

  if (data.nutritionalAdvice) {
    content += `
      <div class="advice">
        <h3>Nutritional Advice</h3>
        <p>${data.nutritionalAdvice}</p>
      </div>
    `;
  }

  Object.entries(data.weekPlan).forEach(([day, meals]: [string, any]) => {
    const dayName = day.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    content += `
      <div class="day">
        <h2>${dayName}</h2>
        <div class="meal">
          <div class="meal-title">Breakfast (${meals.breakfast.calories} cal)</div>
          <div class="food-items">
            ${meals.breakfast.items.map((item: string) => `<div>• ${item}</div>`).join('')}
          </div>
        </div>
        <div class="meal">
          <div class="meal-title">Lunch (${meals.lunch.calories} cal)</div>
          <div class="food-items">
            ${meals.lunch.items.map((item: string) => `<div>• ${item}</div>`).join('')}
          </div>
        </div>
        <div class="meal">
          <div class="meal-title">Dinner (${meals.dinner.calories} cal)</div>
          <div class="food-items">
            ${meals.dinner.items.map((item: string) => `<div>• ${item}</div>`).join('')}
          </div>
        </div>
        <div class="meal">
          <div class="meal-title">Snacks (${meals.snacks.calories} cal)</div>
          <div class="food-items">
            ${meals.snacks.items.map((item: string) => `<div>• ${item}</div>`).join('')}
          </div>
        </div>
      </div>
    `;
  });

  content += `
      <div style="margin-top: 40px; text-align: center; color: #666;">
        <p>Generated on ${new Date().toLocaleDateString()}</p>
        <p>For personalized nutrition consultation, please contact our team.</p>
      </div>
    </body>
    </html>
  `;

  return content;
}
