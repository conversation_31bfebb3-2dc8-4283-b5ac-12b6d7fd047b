/// Represents a single form that an admin can create.
/// For this request, it will hold "fertility meter questions".
model forms {
  id          String      @id @default(uuid()) @db.Uuid
  name        String      @unique
  description String?
  created_at  DateTime    @default(now()) @map("created_at")
  updated_at  DateTime    @updatedAt @map("updated_at")
  slug        String?     @unique
  questions   questions[]
}

/// Represents a single question or field within a form.
/// This table is designed to be flexible enough to handle all specified field types.
model questions {
  id                   String            @id @default(uuid()) @db.Uuid
  form_id              String            @map("form_id") @db.Uuid
  question_text        String            @map("question_text")
  field_type           FormFieldType     @map("field_type")
  placeholder          String?
  min_value            Int?              @map("min_value")
  max_value            Int?              @map("max_value")
  step                 Int?
  unit                 String?
  order                Int
  created_at           DateTime          @default(now()) @map("created_at")
  updated_at           DateTime          @updatedAt @map("updated_at")
  scoring_config       Json?             @map("scoring_config")
  scoring_type         ScoringType?      @map("scoring_type")
  help_text            String?           @map("help_text")
  is_mandatory         Boolean           @default(false) @map("is_mandatory")
  parent_id            String?           @map("parent_id") @db.Uuid
  collective_formula   String?           @map("collective_formula")
  scoring_mode         ScoringMode       @default(separate) @map("scoring_mode")
  depends_on_option_id String?           @map("depends_on_option_id") @db.Uuid
  options              options[]
  question_tracks      question_tracks[]
  depends_on_option    options?          @relation("DependentQuestions", fields: [depends_on_option_id], references: [id])
  form                 forms             @relation(fields: [form_id], references: [id], onDelete: Cascade)
  parent               questions?        @relation("QuestionSubQuestions", fields: [parent_id], references: [id], onDelete: Cascade)
  sub_questions        questions[]       @relation("QuestionSubQuestions")

  @@index([form_id])
  @@index([scoring_type])
  @@index([scoring_type, form_id])
  @@index([parent_id])
  @@map("questions")
}

/// Represents an option for RADIO_SELECT or DROPDOWN_SELECT field types.
/// For example, for "Menstrual Regularity", options would be "Regular" and "Irregular".
model options {
  id                  String      @id @default(uuid()) @db.Uuid
  question_id         String      @map("question_id") @db.Uuid
  option_text         String      @map("option_text")
  value               String?
  order               Int
  created_at          DateTime    @default(now()) @map("created_at")
  updated_at          DateTime    @updatedAt @map("updated_at")
  score               Float?
  question            questions   @relation(fields: [question_id], references: [id], onDelete: Cascade)
  dependent_questions questions[] @relation("DependentQuestions")

  @@index([question_id])
  @@map("options")
}

/// Represents the different tracks for fertility meter questions
/// T1: No testing done, T2: Fertility tests done, T3: IVF experience
model tracks {
  id              String            @id @default(uuid()) @db.Uuid
  code            String            @unique
  name            String
  description     String
  created_at      DateTime          @default(now()) @map("created_at")
  updated_at      DateTime          @updatedAt @map("updated_at")
  order           Int
  question_tracks question_tracks[]
}

/// Junction table for many-to-many relationship between questions and tracks
model question_tracks {
  id          String    @id @default(uuid()) @db.Uuid
  question_id String    @map("question_id") @db.Uuid
  track_id    String    @map("track_id") @db.Uuid
  created_at  DateTime  @default(now()) @map("created_at")
  question    questions @relation(fields: [question_id], references: [id], onDelete: Cascade)
  track       tracks    @relation(fields: [track_id], references: [id], onDelete: Cascade)

  @@unique([question_id, track_id])
  @@index([question_id])
  @@index([track_id])
}

/// Enum to define the type of form field.
enum FormFieldType {
  INPUT
  NUMBER_INPUT
  RADIO_SELECT
  DROPDOWN_SELECT
  RANGE_SLIDER
  GROUP_QUESTION
}

/// Enum to define the scoring type for IVF Fertility Meter
enum ScoringType {
  range
  single_choice
}

enum ScoringMode {
  separate
  collective
}
