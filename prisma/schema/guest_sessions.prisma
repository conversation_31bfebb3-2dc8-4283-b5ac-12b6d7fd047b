model guest_sessions {
  id              String   @id @default(uuid())
  session_token   String   @unique
  ivf_data        Json     // Store the IVF form data as JSON
  current_step    Int      @default(1)
  selected_track  String?  // Stores the selected track (T1, T2, T3)
  email           String?
  display_name    String?
  phone           String?
  is_verified     <PERSON><PERSON><PERSON>  @default(false)
  status          ivf_assessment_status @default(pending)
  expires_at      DateTime
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@index([session_token])
  @@index([expires_at])
  @@index([email])
}
