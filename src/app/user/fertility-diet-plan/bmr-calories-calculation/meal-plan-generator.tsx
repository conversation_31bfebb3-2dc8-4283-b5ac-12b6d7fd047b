"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { FileText, Download, Calendar, Utensils, Info } from "lucide-react";
import { generateMealPlanPDF } from "@/lib/services/meal-plan-pdf.service";

interface MealPlanGeneratorProps {
  userId: string;
  targetCalories: number;
  condition?: string;
}

export default function MealPlanGenerator({ userId, targetCalories, condition }: MealPlanGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasGenerated, setHasGenerated] = useState(false);

  const getConditionDisplayName = (condition: string) => {
    switch (condition) {
      case 'PCOS': return 'PCOS';
      case 'LOW_AMH': return 'Low AMH';
      case 'INCREASE_FERTILITY_NATURALLY': return 'Increase Fertility Naturally';
      case 'INCREASE_IRON': return 'Increase Iron';
      default: return condition;
    }
  };

  const handleGenerateMealPlan = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const pdfContent = await generateMealPlanPDF(userId);
      
      if (pdfContent) {
        // Create a blob from the HTML content
        const blob = new Blob([pdfContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = `meal-plan-${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL
        URL.revokeObjectURL(url);
        
        setHasGenerated(true);
      } else {
        setError("Failed to generate meal plan. Please try again.");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred while generating the meal plan");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleBookConsultation = () => {
    // This would typically redirect to a booking page or CRM
    window.open('mailto:<EMAIL>?subject=Nutrition Consultation Request', '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Meal Plan Generator Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Utensils className="h-5 w-5" />
            Personalized Meal Plan Generator
          </CardTitle>
          <CardDescription>
            Generate a 7-day meal plan based on your BMR calculation and fertility condition
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Status */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Your Current Profile</h4>
                <div className="text-sm text-blue-700 mt-1">
                  <p><strong>Target Calories:</strong> {targetCalories} per day</p>
                  {condition && (
                    <p><strong>Condition:</strong> {getConditionDisplayName(condition)}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex items-center gap-4">
            <Button
              type={ButtonType.PRIMARY}
              text={isGenerating ? "Generating..." : "Generate Meal Plan"}
              icon={isGenerating ? undefined : <FileText className="h-4 w-4" />}
              onClick={handleGenerateMealPlan}
              disabled={isGenerating}
              className="flex-1"
            />
            
            {hasGenerated && (
              <Button
                type={ButtonType.SECONDARY}
                text="Download Again"
                icon={<Download className="h-4 w-4" />}
                onClick={handleGenerateMealPlan}
                disabled={isGenerating}
              />
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-red-700 text-sm">{error}</div>
            </div>
          )}

          {/* Success Message */}
          {hasGenerated && !error && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="text-green-700 text-sm">
                ✅ Meal plan generated successfully! Check your downloads folder.
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* What's Included Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            What's Included in Your Meal Plan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">7-day comprehensive meal plan</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Breakfast, lunch, dinner, and snacks</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Calorie counts for each meal</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Condition-specific nutritional advice</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Fertility-focused food recommendations</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm">Downloadable PDF format</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Consultation CTA */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardHeader>
          <CardTitle className="text-purple-900">Need Personalized Guidance?</CardTitle>
          <CardDescription className="text-purple-700">
            Book a consultation with our nutrition specialist for personalized meal planning and fertility nutrition advice.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            type={ButtonType.PRIMARY}
            text="Book Nutrition Consultation"
            onClick={handleBookConsultation}
            className="bg-purple-600 hover:bg-purple-700"
          />
        </CardContent>
      </Card>
    </div>
  );
}
