"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { FileText, ArrowLeft, Save } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function CreateMealPlanPage() {
  const router = useRouter();
  const [condition, setCondition] = useState("");
  const [targetCaloriesMin, setTargetCaloriesMin] = useState("");
  const [targetCaloriesMax, setTargetCaloriesMax] = useState("");
  const [nutritionalAdvice, setNutritionalAdvice] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log("Saving meal plan:", {
      condition,
      targetCaloriesMin: parseInt(targetCaloriesMin),
      targetCaloriesMax: parseInt(targetCaloriesMax),
      nutritionalAdvice
    });
    
    router.push("/admin/diet-plans");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href="/admin/diet-plans">
            <Button
              type={ButtonType.SECONDARY}
              size="sm"
              text="Back"
              icon={<ArrowLeft className="h-4 w-4" />}
            />
          </Link>
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create Meal Plan</h1>
            <p className="text-muted-foreground">
              Create a 7-day meal plan for a specific fertility condition
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Basic Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Set the condition and calorie range for this meal plan
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Condition</label>
                <select
                  value={condition}
                  onChange={(e) => setCondition(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  required
                >
                  <option value="">Select Condition</option>
                  <option value="PCOS">PCOS</option>
                  <option value="LOW_AMH">Low AMH</option>
                  <option value="INCREASE_FERTILITY_NATURALLY">Increase Fertility Naturally</option>
                  <option value="INCREASE_IRON">Increase Iron</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Min Calories</label>
                <input
                  type="number"
                  value={targetCaloriesMin}
                  onChange={(e) => setTargetCaloriesMin(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="1200"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Max Calories</label>
                <input
                  type="number"
                  value={targetCaloriesMax}
                  onChange={(e) => setTargetCaloriesMax(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="1800"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Nutritional Advice</label>
              <textarea
                value={nutritionalAdvice}
                onChange={(e) => setNutritionalAdvice(e.target.value)}
                className="w-full px-3 py-2 border rounded-md h-24"
                placeholder="Enter nutritional advice for this condition..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-3 mt-6">
          <Link href="/admin/diet-plans">
            <Button
              type={ButtonType.SECONDARY}
              text="Cancel"
            />
          </Link>
          <Button
            type={ButtonType.PRIMARY}
            text="Save Meal Plan"
            icon={<Save className="h-4 w-4" />}
            onClick={() => handleSubmit}
          />
        </div>
      </form>
    </div>
  );
}
